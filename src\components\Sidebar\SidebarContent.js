import React, { useContext } from "react";
import routes from "../../routes/sidebar";
import { NavLink } from "react-router-dom";
import * as Icons from "../../icons";
import { AuthContext } from "../../context/AuthContext";
import { roleService } from "../../services/roles.service";
import { useQuery } from "react-query";
import { DataContext } from "../../context/DataContext";

function Icon({ icon, ...props }) {
  const Icon = Icons[icon];
  return <Icon {...props} />;
}

function SidebarContent() {
  const { user, setRoles, roles } = useContext(AuthContext);
  const { handleReset } = useContext(DataContext);
  const userId = user.roleId;

  useQuery(["/rolesDetails"], () => roleService.getById(userId), {
    enabled: !!userId,
    onSuccess: (data) => {
      setRoles(data?.data);
    },
    refetchOnWindowFocus: true,
  });

  let filteredRoutes = [];

  if (roles !== null) {
    const { resources, staticReports } = roles;

    filteredRoutes = routes.filter((route) => {
      return resources?.some((res) => {
        if (route.name === "Offline Downloads") {
          const staticReportHasDownload = staticReports?.some(
            (report) => report.permissions.download === 1
          );

          const resourceHasCdrDownload = resources?.some(
            (resource) =>
              resource.name === "CDR Search" &&
              resource.permissions.download === 1
          );

          return staticReportHasDownload || resourceHasCdrDownload;
        }

        return (
          res.name === route.roleName && res.permissions[route.permission] === 1
        );
      });
    });
  }

  return (
    <div className="flex flex-col bg-bgPrimary overflow-hidden">
      <div
        style={{
          color: "white",
          marginLeft: "10px",
        }}
      >
        {filteredRoutes.map((route, i) =>
          route.name ? (
            <NavLink
              exact
              to={route.path}
              className="px-4 py-4 border-b border-white w-[95px] text-sm font-semibold transition-colors duration-150 hover:text-gray-800 dark:hover:text-gray-200 flex flex-col items-center"
              activeClassName="text-gray-800 dark:text-gray-100"
              key={route.name}
              style={({ isActive }) => ({
                backgroundColor: isActive ? "gray" : "#545151",
                borderRadius: "2px",
              })}
              onClick={() => {
                handleReset();
              }}
            >
              <Icon
                aria-hidden="true"
                icon={route.icon}
                style={{ height: "20px", marginBottom: "10px" }}
              />
              <span className="mt-1 text-center text-xs font-normal mb-2">
                {route.name}
              </span>
            </NavLink>
          ) : null
        )}
      </div>
    </div>
  );
}

export default SidebarContent;
