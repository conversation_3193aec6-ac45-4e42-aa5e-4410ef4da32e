//import { t } from "i18next";

export const convertToDateObject = (dateString) => {
  let date, month, year;
  if (dateString) {
    let newFormat = dateString.split("-");
    if (newFormat.length === 2) {
      month = newFormat[0];
      year = newFormat[1];
    } else if (newFormat.length === 3) {
      date = newFormat[0];
      month = newFormat[1];
      year = newFormat[2];
    }
    return new Date(year, month - 1);
  }
};

/////////////////////Styles///////////////////////
export const templates = {
  candidate: "Candidate",
  recruiter: "Recruiter",
  platformAdmin: "Platform Admin",
};
////////////////////////////////////////////////////

export const commonRegex = /^[^<>+$;'"\\/~|:]+$/;
export const specialRegex = /^[^<>$\\~|]+$/;
export const passwordRegex = /[!@^*+()_=\\,.?\\-]+/;
export const passwordRestrictedRegex = /^[^<>&;$#%"'`/:|[\]{}]+$/;
export const onlySpaceRegex = /(?=.*[a-z])|(?=.*[A-Z])/;
export const alphanumericRegex = /^(?=.*[a-zA-Z])(?=.*\d).+$/;
export const linkedinRegex =
  /https:\/\/(?:[a-z]+\.)?linkedin\.com\/[a-zA-Z0-9-]+/;
export const websiteRegex =
  /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/;

export const alphaAndSpaceRegex = /^[A-Za-z][A-Za-z ]*$/;
export const onlyNumericsRegex = /^\d+$/; // only numbers are allowed

export const onlyNumSpaceNotAllowed =
  /^(?=.*[a-zA-Z])[a-zA-Z0-9_\s!@#$%^&*()\-+=~"`'<>,./?;:{}[\]|\\]*$/; //Only space,only special char,only numerics not allowed.Alphabets with space,numbers and special chars allowed.

//export const websiteRegex = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/\S*)?$/;
export const passwordAllowedCharacters = " !@^*+()_=\\,.?\\-";
export const onlyZerosNotAllowedRegex = /^(?!0+$)[0-9]*$/;
export const onlyZerosRegex = /^(?!0+$)[0-9A-Za-z]/;
export const invalidPINRegex =
  /^(?![-\s])(?![A-Za-z]+$)(?=.*[0-9])[0-9A-Za-z\s-]*$/;

export const panelOptions = [
  { value: "line", label: "Line" },
  { value: "bar", label: "Bar" },
  { value: "table", label: "Table" },
  { value: "multi", label: "Multi-axis" },
  { value: "pie", label: "Pie" },
];

export const recordsPanel = [];
for (let i = 5; i <= 50; i += 5) {
  recordsPanel.push({ value: i.toString(), label: i.toString() });
}

export const logicalOperator = [
  { value: "and", label: "AND" },
  { value: "or", label: "OR" },
  { value: "no", label: "NO" },
];

export const alertTypeOptions = [
  { label: "Delivery Drop", value: "Delivery Drop" },
  { label: "Error", value: "Error" },
  { label: "Volume Drop Or Spike", value: "Volume Drop or Spike" },
  { label: "Delivery Report Pending", value: "Delivery Report Pending" },
];

export const filterOptions = [
  { label: "Supplier", value: "Supplier" },
  { label: "Customer", value: "Customer" },
  { label: "Destination", value: "Destination" },
  { label: "Supplier-Destination", value: "Supplier-Destination" },
  { label: "Customer-Destination", value: "Customer-Destination" },
  {
    label: "Customer-Supplier-Destination",
    value: "Customer-Supplier-Destination",
  },
];

export const vloumeDropFilterOptions = [
  { label: "Customer", value: "Customer" },
  { label: "Destination", value: "Destination" },

  { label: "Customer-Destination", value: "Customer-Destination" },
];

export const alertCategoryOptions = [
  { label: "Major", value: "Major" },
  { label: "Minor", value: "Minor" },

  { label: "Critical", value: "Critical" },
];

export const deliveryReportDropFilterOptions = [
  { label: "Customer", value: "Customer" },

  { label: "Customer-Destination", value: "Customer-Destination" },
  { label: "Supplier", value: "Supplier" },
  { label: "Supplier-Destination", value: "Supplier-Destination" },
];

export const timePeriodOptions = [
  { label: "Yesterday", value: 1 },
  { label: "Last three days", value: 3 },
  { label: "Last five days", value: 3 },
  { label: "Last weekdays", value: 7 },
];

export const volumeTypeOptions = [
  { label: "Spike", value: "spike" },
  { label: "Drop", value: "drop" },
  { label: "Both", value: "both" },
];

export const timeIntervalOptions = [
  { label: "15 min", value: 15 },
  { label: "30 min", value: 30 },
  { label: "45 min", value: 45 },
  { label: "1 hour", value: 1 },
];

export const timeAcessOptions = {
  minute: ["Last Hour", "Calendar"],
  hour: [
    "Last Hour",
    "Today",
    "Yesterday",
    "Last 6 Hours",
    "Last 12 Hours",
    "Last 24 Hours",
    "Calendar",
  ],
  day: [
    "Last Seven Days",
    "Last Week",
    "Today",
    "Calendar",
    "Yesterday",
    "This Year",
    "Last Year",
  ],
  week: ["Last Seven Days", "Last Week", "Calendar", "This Year", "Last Year"],
  month: [
    "Last 30 Days",
    "This Month",
    "Last Month",
    "Calendar",
    "This Year",
    "Last Year",
  ],
};

export const defaultTimeRange = [
  "Last Hour",
  "Today",
  "Yesterday",
  "Last 6 Hours",
  "Last Seven Days",
  "Last 12 Hours",
  "Last Week",
  "Last 24 Hours",
  "Last 30 Days",
  "This Month",
  "Last Month",
  "Calendar",
];

export const protocolTypeOptions = [
  { label: "Select All", value: "Select All" },
  { label: "A2P", value: "A2P" },
  { label: "P2P", value: "P2P" },
];

export const interfaceTypeOptions = [
  { label: "Select All", value: "Select All" },
  { label: "SMPP", value: "SMPP" },
  { label: "SS7", value: "SS7" },
];

export const ConditionDisplay = ({ conditions }) => {
  return (
    <>
      {conditions?.map((conditionGroup, index) => {
        let conditionString;

        if ("type1" in conditionGroup) {
          conditionString = Object.entries(conditionGroup)
            .map(([key, value]) => {
              if (key.startsWith("type")) {
                return value;
              } else {
                return `{${value}}`;
              }
            })
            .filter(Boolean)
            .join(" ");
        } else {
          const {
            field,
            condition: conditionType,
            value,
            operator,
          } = conditionGroup;

          conditionString = [
            field,
            conditionType,
            value,
            operator && `{${operator}}`,
          ]
            .filter(Boolean)
            .join(" ");
        }

        if (conditionGroup.type4) {
          conditionString = conditionString.replace(
            conditionGroup.type4,
            `{${conditionGroup.type4}}`
          );
        }

        if (index === conditions.length - 1 && conditionGroup.type4) {
          conditionString = conditionString.replace(
            `{${conditionGroup.type4}}`,
            ""
          );
        }
        if (index === conditions.length - 1 && conditionGroup.operator) {
          conditionString = conditionString.replace(
            `{${conditionGroup.operator}}`,
            ""
          );
        }

        const isEmpty = Object.values(conditionGroup).every(
          (value) => value === ""
        );

        return (
          <div key={index} className="mb-1">
            <span className={`rounded-md p-1 text-xs ml-1`}>
              {/* {console.log("formData.conditions", conditions)} */}
              {isEmpty ? "No Filters added" : conditionString}
            </span>
          </div>
        );
      })}
    </>
  );
};
export const default_file_type = "CSV";
export const customerInterface = [
  { value: "SMPP", label: "SMPP" },
  { value: "SS7", label: "SS7" },
  { value: "SMPP_ES", label: "SMPP_ES" },
];
export const customerProtocol = [
  { value: "A2P", label: "A2P" },
  { value: "P2P", label: "P2P" },
];
export const destinationMsg = [
  { value: "AT", label: "AT" },
  { value: "MT", label: "MT" },
  { value: "IN", label: "IN" },
  { value: "CF", label: "CF" },
];
export const trafficType = [
  { value: "A2P", label: "A2P" },
  { value: "P2P", label: "P2P" },
];
export const supplierProtocol = [
  { value: "A2P", label: "A2P" },
  { value: "P2P", label: "P2P" },
  { value: "NA", label: "NA" },
];

export const transactionType = [
  { value: "Select All", label: "Select All" },
  { label: "SMPP to SMPP", value: "AO-AT, DR-AT" },
  { label: "SMPP to SS7", value: "AO-MT, DR-MT" },
  { label: "SS7 to SS7", value: "MT-IN, MT-CF, RI-IN, RI-CF" },
];

export const dynamicReports = {
  Customer: "Customer Wise Revenue Report",
  Supplier: "Supplier Wise Cost Report",
  Traffic: "WhatsApp Traffic Report",
  Facebook: "Facebook Traffic Report",
  SlabBasedCust: "Slab Based Billing Report (Customer)",
  SlabBasedSupp: "Slab Based Billing Report (Supplier)",
  SupplierMultiple: "Supplier Wise Cost Report Multiple",
  CustomerMutiple: "Customer Wise Revenue Report Multiple",
};

export const defaultTimeZone = [
  { label: "GMT+05:30 - Asia - Kolkata", value: "Asia/Kolkata" },
];

export const inspect_mode_enable = true;

export const CDRSearch_conditional_filter = 2;

export const filterLabelMap = {
  customer_name: "Customer Name",
  customer_bind: "Customer Bind",
  src_prime: "Source Prime",
  destination: "Destination",
  dest_prime: "Destination Prime",
  supplier: "Supplier Name",
  supplier_name: "Supplier Name",
  supplier_bind: "Supplier Bind",
  destination_operator_name: "Destination Operator Name",
  destination_country_name: "Destination Country Name",
  customer_interface_type: "Customer Interface Type",
  supplier_interface_type: "Supplier Interface Type",
  customer_billing_logic: "Customer Billing Logic",
  supplier_billing_logic: "Supplier Billing Logic",
  customer_traffic_type: "Customer Traffic Type",
  supplier_traffic_type: "Supplier Traffic Type",
  destination_mcc_final: "Destination MCC",
  destination_mnc_final: "Destination MNC",
  lcr_name: "LCR Name",
  spec_lcr: "Spec LCR",
  status: "Status",
  customer_kam: "Customer KAM",
  supplier_kam: "Supplier KAM",
  roamingDirectStatus: "Roaming and Direct Status",
  bilateral: "Bilateral",
  negative_report: "Negative Report",
};
