import { ErrorMessage, useField, useFormikContext } from "formik";
import ReactSelect, { components, createFilter } from "react-select";
import { useState } from "react";

const Select = ({
  fontSize,
  isDisabled,
  options,
  menuListzIndex,
  handleUpdate,
  ...props
}) => {
  const [field, meta, helpers] = useField(props.name);
  const { setFieldTouched } = useFormikContext();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Custom styles for the select dropdown
  const customSelectStyles = {
    control: (baseStyles) => ({
      ...baseStyles,
      fontWeight: "400",
      fontSize: fontSize || "12px",
      minHeight: "40px",
      borderColor: isDisabled ? "#D9D9D9" : baseStyles.borderColor,
      pointerEvents: isDisabled ? "none" : baseStyles.pointerEvents,
      borderWidth: "1px",
      border:
        meta.error && meta.touched ? "1px solid #C8C0C0" : "1px solid #C8C0C0",
      borderRadius: "5px",
      "&:hover": {
        border: "1px solid #C8C0C0",
      },
      boxShadow: "none",
    }),
    menu: (styles) => ({
      ...styles,
      backgroundColor: "white",
      fontSize: fontSize || "12px",
      position: "absolute",
      minWidth: "100%",
      border: "1px solid #70707059",
      boxShadow: "0 !important",
      "&:hover": {
        border: "1px solid #70707059",
      },
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isFocused ? "#f2f2f2" : "white", // Change hover background color here
      color: state.isFocused ? "#333333" : "#2D2D2D", // Change hover text color here
      ":hover": {
        backgroundColor: "#f2f2f2", // Remove the default blue hover color
      },
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: menuListzIndex || "auto",
    }),
  };

  // Custom Option with radio buttons
  const CustomOption = (props) => {
    const { data, innerRef, innerProps, isSelected } = props;
    return (
      <div ref={innerRef} {...innerProps} className="flex items-center p-1">
        <input
          type="radio"
          checked={isSelected}
          onChange={() => {}}
          className="mx-2"
        />
        <components.Option {...props} />
      </div>
    );
  };

  // Custom Menu component with Update and Cancel buttons
  const CustomMenu = (props) => {
    return (
      <components.Menu {...props}>
        {props.children}
        <div className="p-2 border-t border-[#70707059] flex justify-between">
          <button
            className="px-3 py-1 border border-[#DC3833] rounded-md text-[#DC3833]"
            onClick={() => {
              helpers.setValue(props.isMulti ? [] : null);
              setIsMenuOpen(false);
            }}
          >
            Cancel
          </button>
          <button
            className="px-3 py-1 bg-[#DC3833] text-white rounded-md"
            onClick={() => {
              setIsMenuOpen(false);
              handleUpdate(field.value);
            }}
          >
            Update
          </button>
        </div>
      </components.Menu>
    );
  };

  return (
    <div className="relative">
      <ReactSelect
        aria-label={props.name}
        className={`input ${props.className} ${
          meta.touched && meta.error ? "is-danger" : ""
        }`}
        options={options}
        menuPortalTarget={menuListzIndex ? document.body : null}
        isSearchable={props.isSearchable || false}
        styles={customSelectStyles}
        isMulti={props.isMulti}
        components={{ Option: CustomOption, Menu: CustomMenu }}
        filterOption={createFilter({ ignoreAccents: false })}
        isClearable={props.isClearable}
        maxMenuHeight={props.maxMenuHeight}
        name={props.name}
        isDisabled={isDisabled}
        isLoading={props.isLoading}
        value={
          props.isMulti
            ? options.filter((option) => field.value.includes(option.value))
            : options.find((option) => option.value === field.value) || null
        }
        onChange={(option) => {
          setFieldTouched(props.name, true);
          if (props.onChange) props.onChange(option);
          if (!props.isMulti) {
            helpers.setValue(option?.value || "");
          } else {
            const selectedValues = option ? option.map((opt) => opt.value) : [];
            helpers.setValue(selectedValues);
          }
        }}
        onMenuOpen={() => setIsMenuOpen(true)}
        onMenuClose={() => setIsMenuOpen(false)}
        onBlur={() => setFieldTouched(props.name, true)}
        placeholder={props.placeholder}
        menuIsOpen={isMenuOpen}
      />
      <ErrorMessage
        component="div"
        className="text-[#d32f2f] text-[0.75rem] mt-0 font-normal"
        name={field.name}
      />
    </div>
  );
};

export default Select;
