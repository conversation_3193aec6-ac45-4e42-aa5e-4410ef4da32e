///This file is created to import the configurations of colors and styles in application
/** @type {import('tailwindcss').Config} */

module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  important: "#root",
  theme: {
    extend: {
      backgroundImage: {
        logo: "url('../src/assets/img/Airtel 1.svg')",
      },
      backgroundColor: {
        bgPrimary: "#F3F8FF",
        bgSecondary: "#DC3833",
        bgTeritary: "#F28F8F80",
        bgouterBackground: "#E0DDDD",
        selectBackground: "#DC38330D",
        sidebarMenu: "#D9D9D9",
        bgHeader: "#FFFFFF",
        bgSidebar: "#545151",
        rowHoverBackground: "#F2F2F2",
        tableHeader: "#E7F1FD",
        toolTipBackground: "#EAEAEB",
        bgCheckboxSelection: "#707070",
        bgTable: "#DDE9FD",
        bgTableHeader: "#9C9898",
      },
      borderColor: {
        listBorder: "#F4F4F4",
        outerBorder: "#E0DDDD",
        panelBorder: "#d9d9d9",
        errorBorder: "#DC3833",
        tabColor: "#9C9898",
        calendarBoreder: "#C8C0C0",
        inputTextBoxBorder: "C8C0C0",
        tableBorder: "#C8D4DD",
        tableCellBorder: "#ddd",
      },
      textColor: {
        dialogColor: "#C8C0C0",
        headingColor: " #393939",
        tabColor: "#9C9898",
        errorColor: "#DC3833",
        titleColor: "#707070",
        tooltipTextColor: "#656262",
        tabTextColor: "#808080",
      },
    },
  },
  plugins: [],
};
