import React, { useState, useContext } from "react";
import {
  getAll,
  getDashboardById,
  deleteDashboard,
} from "../../services/dashboard-api";
import { useQuery, useMutation } from "react-query";
import { EditingIcon, DeleteIcon, EditIcon } from "../../icons";
import { previewPanel } from "../../services/panels-api";
import ResponsiveLayout from "./ResponsiveLayout";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import SuccessDialog from "../../popups/SuccessDialog";
import ErrorDialog from "../../popups/ErrorDialog";
import DeleteDialog from "../../popups/DeleteDialog";
import InfoModal from "../../components/modals/InfoModal";
import { AuthContext } from "../../context/AuthContext";
import Title from "../../Title";
import BreadcrumbNavigation from "../../components/BreadCrumps/BreadCrump";
import dayjs from "dayjs";
import { convertToUTC } from "../../common/commonFunctions";
import customParseFormat from "dayjs/plugin/customParseFormat";
import ResponsiveLayoutCard from "./ResponsiveLayoutCard";
import { MetaDataProvider } from "../../context/MetaDataContext";
import StaticCollapsibleFilter from "../../components/CollapsibleFilter/StaticCollapsibleFilter";

function MyDashboard({ dashboardId, isNoDownload }) {
  const parms = useParams();
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [suceessDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [cardDroppedData, setCardDroppedData] = useState([]);
  const [previewData, setPreviewData] = useState([]);
  const [previewResponses, setPreviewResponses] = useState([]);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [errorCode, setErrorCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const listData = location?.state?.isEdit;
  const createdBy = location?.state?.createdBy;

  const value = parms.id;
  const { roles } = useContext(AuthContext);
  dayjs.extend(customParseFormat);
  const permissions = roles?.resources?.filter(
    (res) => res?.name === "Dashboard Management"
  )[0]?.permissions;

  const handleCloseInfoModal = () => {
    setShowAlertConfirmation(false);
  };

  const { data: dashboardList, isLoading: isloading } = useQuery(
    ["getDashboardById", value || dashboardId],
    getDashboardById,
    {
      onSuccess: ({ data }) => {
        setCardDroppedData(
          data?.cards.map((card) => ({
            reportField: card.cardDetails.reportField,
            value: card.cardDetails.value,
            order: card.order,
          }))
        );
        const panels = data?.panels?.map((panel, i) => {
          let dimension = {
            w: panel.order > 1 ? 3 : 6,
            h: 3,
            x: 0,
            y: 0,
            order: panel.order,
          };

          return { ...panel, dimension };
        });
        setPreviewResponses(panels);
        setPreviewData(panels);
        previewAPICall(panels);
      },
      onError: ({ response }) => {
        //console.log("resp", response.data.message);
        setErrorMsg(response.data.message);
      },

      refetchOnWindowFocus: false,
    }
  );

  const dashboardName = dashboardList?.data?.name;

  const previewAPICall = (previewData) => {
    setLoading(true);
    previewData.forEach((payload, index) => {
      const selectedRange = payload.panelDetails.timePeriod;
      //console.log("payload", payload);
      let formattedStart = "";
      let formattedEnd = "";
      const currentDateTime = dayjs();

      if (selectedRange) {
        if (selectedRange.includes("to")) {
          const [startString, endString] = selectedRange.split("to");
          formattedStart = startString;
          formattedEnd = endString;
        } else {
          if (
            selectedRange === "Last Hour" ||
            selectedRange === "Last 6 Hours" ||
            selectedRange === "Last 12 Hours" ||
            selectedRange === "Last 24 Hours"
          ) {
            const hours = {
              "Last Hour": 1,
              "Last 6 Hours": 6,
              "Last 12 Hours": 12,
              "Last 24 Hours": 24,
            };

            const lastXHours = currentDateTime.subtract(
              hours[selectedRange],
              "hour"
            );
            if (selectedRange === "Last Hour") {
              formattedStart = lastXHours.format("YYYY-MM-DD HH:mm:ss");
              formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
            } else {
              formattedStart = lastXHours.format("YYYY-MM-DD HH:00:00");
              formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
            }
          } else if (selectedRange === "Today") {
            formattedStart = currentDateTime
              .startOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
          } else if (selectedRange === "Yesterday") {
            const yesterday = currentDateTime.subtract(1, "day");
            formattedStart = yesterday
              .startOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss");
          } else if (selectedRange === "Last Seven Days") {
            formattedStart = currentDateTime
              .subtract(6, "days")
              .startOf("day")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "Last Week") {
            formattedStart = currentDateTime
              .subtract(1, "week")
              .startOf("week")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .subtract(1, "week")
              .endOf("week")
              .format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "Last 30 Days") {
            formattedStart = currentDateTime
              .subtract(29, "days")
              .startOf("day")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .endOf("day")
              .format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "Last Month") {
            formattedStart = currentDateTime
              .subtract(1, "month")
              .startOf("month")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .subtract(1, "month")
              .endOf("month")
              .format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "This Month") {
            formattedStart = currentDateTime
              .startOf("month")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime
              .endOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
          }
        }
      }

      let reqData = {
        name: payload.panelDetails.name,
        visualizationType: payload.panelDetails.visualizationType,
        filters: [],
        dataColumns: {
          derivedFields: payload.panelDetails.dataColumns?.derivedFields,
        },
        // startDate: convertToUTC(formattedStart),
        // endDate: convertToUTC(formattedEnd),
        startDate: formattedStart,
        endDate: formattedEnd,
      };

      let reqDataForDashBoard = {
        name: payload.panelDetails.name,
        visualizationType: payload.panelDetails.visualizationType,
        filters: [],
        dataColumns: {
          derivedFields: payload.panelDetails.dataColumns?.derivedFields,
        },
        startDate: formattedStart,
        endDate: formattedEnd,
      };
      payload.panelDetails.filters.forEach((condition) => {
        reqDataForDashBoard.filters.push({
          field: condition.field,
          condition: condition.condition,
          value: condition.value,
          operator: condition.operator,
        });
      });

      if (payload.panelDetails.visualizationType === "Bar Graph") {
        reqDataForDashBoard.dataColumns["X-Axis"] =
          payload.panelDetails.dataColumns?.["X-Axis"];
        reqDataForDashBoard.dataColumns.noOfRecords =
          payload.panelDetails.dataColumns.noOfRecords;
      } else {
        reqDataForDashBoard.dataColumns.tableFields =
          payload.panelDetails.dataColumns?.tableFields;
      }
      if (payload.panelDetails.visualizationType === "Bar Graph") {
        reqData.dataColumns["X-Axis"] =
          payload.panelDetails.dataColumns?.["X-Axis"];
        reqData.dataColumns.noOfRecords =
          payload.panelDetails.dataColumns.noOfRecords;
      } else {
        reqData.dataColumns.tableFields =
          payload.panelDetails.dataColumns?.tableFields;
      }

      payload.panelDetails.filters.forEach((condition) => {
        reqData.filters.push({
          field: condition.field,
          condition: condition.condition,
          value: condition.value,
          operator: condition.operator,
        });
      });
      previewPanel({ reqData })
        .then(({ data }) => {
          setLoading(false);
          // console.log("count", data);
          setPreviewResponses((prevResponses) => {
            let newData = prevResponses?.filter(
              ({ dimension }) => dimension.order !== payload.dimension.order
            );

            newData.push({
              data: data.data,
              panelData: {
                ...reqDataForDashBoard,
                data:
                  reqDataForDashBoard.visualizationType === "MultiAxis Graph"
                    ? data
                    : data?.data,
              },
              dimension: payload.dimension,
              id: payload.panelDetails.id,
              timePeriod: payload.panelDetails.timePeriod,
              count: data.count,
            });
            return newData;
          });
        })
        .catch((err) => {
          //setErrorCode(err?.message);
          setLoading(false);
          setPreviewResponses((prevResponses) => {
            let newData = prevResponses?.filter(
              ({ dimension }) => dimension.order !== payload.dimension.order
            );

            newData.push({
              data: [],
              panelData: {
                ...reqDataForDashBoard,
                data: "failed",
              },
              dimension: payload.dimension,
              id: payload.panelDetails.id,
              timePeriod: payload.panelDetails.timePeriod,
            });
            return newData;
          });
        });
    });
  };

  const { mutate: DeleteDashboard, isLoading: deleteLoading } =
    useMutation(deleteDashboard);
  const getGridData = (index, dimension) => {
    let dim = { x: 0, y: 0, w: dimension.w, h: dimension.h };

    switch (dimension.order - 1) {
      case 0:
        return dim;
      case 1:
        dim.y = 3;
        return dim;
      case 2:
        dim.x = 6;
        return dim;
      case 3:
        dim.y = 3;
        return dim;
      case 4:
        dim.x = 6;
        dim.y = 3;
        return dim;
      default:
        return dim;
    }
  };
  const getPermission = () => {
    if (!createdBy) {
      return true;
    }

    let data = roles?.dynamicDashboard.filter((x) => {
      return x.name === dashboardList?.data?.name;
    });

    return data?.[0]?.permissions?.download === 1 ? true : false;
  };

  return (
    <>
      {isloading ? (
        <h1 className="w-full h-screen flex justify-center items-center text-center p-6">
          {" "}
          Loading ...{" "}
        </h1>
      ) : errorCode === "Network Error" && !loading ? (
        <h1 className="w-full h-screen flex justify-center items-center text-center p-6">
          {" "}
          Something went wrong! Please check the connectivity!{" "}
        </h1>
      ) : (
        <>
          {" "}
          <div className="my-6 flex justify-between items-center">
            <div className="text-headingColor text-2xl font-bold cursor-pointer">
              {dashboardId ? (
                <>
                  <Title title={"Dashboard"} />
                </>
              ) : (
                <BreadcrumbNavigation
                  //linkOne={"Roles"}
                  linkTwo={"Dashboard"}
                  onlinkTwoClick={() => navigate("/app/dashboard/details")}
                  title={dashboardName}
                />
                // <span onClick={() => navigate("/app/dashboard/details")}>
                //   <Title
                //     title={
                //       " < My Dashboard - " +
                //       (dashboardName ? dashboardName : "")
                //     }
                //   />
                // </span>
              )}
            </div>

            {!dashboardId && !listData ? (
              <div className="bg-white p-2.5 rounded md:w-[220px] w-full">
                <div className="border border-panelBorder p-2 rounded flex items-center justify-between">
                  <div
                    className="text-sm"
                    style={{
                      whiteSpace: "pre-wrap",
                      wordWrap: "break-word",
                      maxWidth: "124px",
                    }}
                  >
                    {dashboardName}
                  </div>
                  <div className="flex items-center">
                    <EditingIcon
                      className="w-3.5 h-3.5 ml-2 text-gray-500 cursor-pointer"
                      onClick={() => {
                        if (permissions.update === 0 || createdBy) {
                          setShowAlertConfirmation(true);
                          setMessage("Update permission not allowed");
                        } else {
                          navigate(`/app/dashboard/details/edit/${value}`, {
                            state: { isEdit: true },
                          });
                        }
                      }}
                    />
                    <DeleteIcon
                      className="w-3.5 h-3.5 ml-2 text-gray-500 cursor-pointer"
                      onClick={(e) => {
                        if (permissions.delete === 0 || createdBy) {
                          setShowAlertConfirmation(true);
                          setMessage("Delete permission not allowed");
                        } else {
                          setDeleteDialog(true);
                        }
                      }}
                    />
                  </div>
                </div>
              </div>
            ) : null}
          </div>
          <MetaDataProvider>
            <StaticCollapsibleFilter />
          </MetaDataProvider>
          <div className="">
            {errorMsg ? (
              <div className="text-center md:mt-36 font-semibold text-xl">
                {errorMsg}
              </div>
            ) : null}
          </div>
          <div className="-ml-3 w-[80vw] h-[82%] relative">
            {dashboardList?.data?.cards?.length === 0 &&
            dashboardList?.data?.panels.length === 0 ? (
              <div className="text-center md:mt-36 font-semibold">
                No cards or panels have been created for this dashboard
              </div>
            ) : (
              <>
                <ResponsiveLayoutCard cardDroppedData={cardDroppedData} />
                <ResponsiveLayout
                  cardDroppedData={cardDroppedData}
                  panelDroppedData={previewResponses}
                  getGridData={getGridData}
                  //isLoading={loading}
                  //isDownload={download}
                  // isDownload={
                  //   isNoDownload
                  //     ? false
                  //     : roles?.isSuperAdminRole
                  //     ? true
                  //     : getPermission()
                  // }
                  isDownload={roles?.isSuperAdminRole ? true : getPermission()}
                  setPreviewLoading={setPreviewLoading}
                  previewLoading={previewLoading}
                />
              </>
            )}
          </div>
        </>
      )}
      <DeleteDialog
        show={deleteDialog}
        onHide={() => setDeleteDialog(false)}
        onConfirm={() => {
          DeleteDashboard(
            { value },
            {
              onSuccess: () => {
                setDeleteDialog(false);
                setMessage("Dashboard deleted successfully");
                setSuccessDialog(true);
                navigate("/app/dashboard/details");
              },
              onError: (error) => {
                setDeleteDialog(false);
                setMessage(error?.response?.data?.message);
                setErrorDialog(true);
              },
            }
          );
        }}
        title={"Are you sure to delete the dashboard ?"}
        isLoading={deleteLoading}
      />
      <SuccessDialog
        show={suceessDialog}
        onHide={() => setSuccessDialog(false)}
        message={message}
      />
      <ErrorDialog
        show={errorDialog}
        onHide={() => setErrorDialog(false)}
        message={message}
      />
      <InfoModal
        show={showAlertConfirmation}
        onHide={handleCloseInfoModal}
        message={message}
      />
    </>
  );
}

export default MyDashboard;
