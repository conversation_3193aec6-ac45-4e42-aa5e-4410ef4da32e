import React, { useContext, useState, useEffect, useRef } from "react";
import { SidebarContext } from "../context/SidebarContext";
import { AuthContext } from "../context/AuthContext";
import Avatar from "react-avatar";
import theme from "../tailwind-theme";
import { MenuIcon } from "../icons";

function Header() {
  const { toggleSidebar } = useContext(SidebarContext);
  const { logout, user, storeLoginTime } = useContext(AuthContext);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);

  const [isAlertsMenuOpen, setIsAlertsMenuOpen] = useState(false);
  const [notifyCounts, setNotifyCounts] = useState({
    critical: 0,
    major: 0,
    minor: 0,
  });

  const alertMenuRef = useRef(null);

  function handleAlertsClick() {
    setIsAlertsMenuOpen((prevState) => !prevState);
  }
  function handleProfileClick() {
    setIsProfileMenuOpen(!isProfileMenuOpen);
  }
  // useQuery(["/alertNotify"], getAlertNotify, {
  //   refetchOnWindowFocus: false,
  //   onSuccess: (res) => {
  //     setNotifyCounts({
  //       critical: res?.data?.critical || 0,
  //       major: res?.data?.major || 0,
  //       minor: res?.data?.minor || 0,
  //     });
  //   },
  // });

  useEffect(() => {
    function handleClickOutside(event) {
      if (
        alertMenuRef.current &&
        !alertMenuRef.current.contains(event.target)
      ) {
        setIsAlertsMenuOpen(false);
      }
    }

    if (isAlertsMenuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isAlertsMenuOpen]);

  return (
    <header className="z-40 py-1 mx-2 bg-bgHeader shadow-bottom dark:bg-gray-800">
      <div className="flex items-center justify-between px-1 mx-auto text-purple-600 dark:text-purple-300">
        {/* Mobile hamburger */}
        <button
          className="p-1 mr-5 -ml-1 rounded-md lg:hidden focus:outline-none focus:shadow-outline-purple"
          onClick={toggleSidebar}
          aria-label="Menu"
        >
          <MenuIcon className="w-6 h-6" aria-hidden="true" />
        </button>
        {/* Logo */}
        <div className="flex flex-1 lg:mr-32">
          <div className="bg-logo h-[62px] w-[100px] bg-contain bg-no-repeat" />
        </div>
        {/* Navigation */}
        <ul className="flex items-center flex-shrink-0 space-x-6 pr-6">
          {/* Alerts menu */}
          <li className="relative" ref={alertMenuRef}>
            {/* <button
              className="relative align-middle rounded-md focus:outline-none focus:shadow-outline-purple"
              onClick={handleAlertsClick}
              aria-label="Notifications"
            >
              <BellIcon className="w-5 h-5" aria-hidden="true" />
            </button> */}
            {/* Alerts popup */}
            {isAlertsMenuOpen && (
              <div className="absolute right-0 mt-2 w-60 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="p-4 border-b border-gray-300 bg-[#f3f8ff]">
                  <h2 className="text-sm font-bold text-gray-700">
                    Recent Open Alerts
                  </h2>
                </div>
                <div className="p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">
                      Critical
                    </span>
                    <span className="text-sm font-bold text-white bg-red-600 w-6 h-6 flex items-center justify-center rounded-full">
                      {notifyCounts?.critical}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">
                      Major
                    </span>
                    <span className="text-sm font-bold text-white bg-green-600 w-6 h-6 flex items-center justify-center rounded-full">
                      {notifyCounts?.major}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">
                      Minor
                    </span>
                    <span className="text-sm font-bold text-white bg-yellow-600 w-6 h-6 flex items-center justify-center rounded-full">
                      {notifyCounts?.minor}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </li>
          {/* Profile menu */}
          <li className="relative">
            <div className="flex items-center mt-2">
              <div className="mx-4   flex flex-col">
                <p className="text-sm text-black font-bold">
                  {" "}
                  Welcome {user?.name}
                </p>
                <p className={`text-[10px] font-normal text-titleColor`}>
                  Login Time : {storeLoginTime}
                </p>
              </div>
              <button
                className="rounded-full focus:shadow-outline-purple focus:outline-none "
                onClick={handleProfileClick}
                aria-label="Account"
              >
                {user?.isSuperAdmin ? (
                  user?.profileImage ? (
                    <img
                      style={{
                        borderRadius: "24px",
                        width: "40px",
                        height: "40px",
                      }}
                      aria-hidden="true"
                      src={user?.profileImage}
                      alt=""
                    />
                  ) : (
                    <Avatar
                      size="35"
                      round={true}
                      color={theme.backgroundColor.bgSecondary}
                      name={user ? user?.name : ""}
                    />
                  )
                ) : user?.profileImage ? (
                  <img
                    style={{
                      borderRadius: "24px",
                      width: "40px",
                      height: "40px",
                    }}
                    aria-hidden="true"
                    src={user?.profileImage}
                    alt="Airtel SMS Hub Reporting"
                  />
                ) : (
                  <Avatar
                    size="35"
                    round={true}
                    color={theme.backgroundColor.bgSecondary}
                    name={user ? user?.name : ""}
                  />
                )}
              </button>
            </div>
            {/* <!-- Profile dropdown --> */}
            {isProfileMenuOpen && (
              <div className="absolute right-0 mt-2 w-44 origin-top-right bg-white divide-y divide-gray-100 rounded-md shadow-md ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                <div className="py-1">
                  {/* <Link
                     to="#"
                     className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                   >
                     Profile
                   </Link>
                   <Link
                    to="#"
                     className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                   >
                     Settings
                   </Link> */}
                  <button
                    onClick={() => logout()}
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                  >
                    Log out
                  </button>
                </div>
              </div>
            )}
          </li>
        </ul>
      </div>
    </header>
  );
}

export default Header;
