import React from "react";
import {
  <PERSON>mposed<PERSON><PERSON>,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

const colors = ["#8BD1AE", "#F28F8F", "#EDDF82", "#A1C3FA"];

const MultiAxisChart = ({ data, dimension, ischart }) => {
  const legendWrapperStyle = {
    position: "absolute",
    top: "5%",
    // left: "20%",
    transform: "translateY(-80%)",
    fontWeight: 400,
    fontSize: "12px",
  };

  function convertDatetimeFormat(datetime) {
    const date = new Date(datetime);
    const formattedDate = date.toLocaleString("en-US", {
      month: "short",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
    return formattedDate.replace(",", "");
  }

  // Convert sampledata to convertedData
  const convertedData = {
    ...data,
    data: data?.data?.map((item) => ({
      ...item,
      Datetime: convertDatetimeFormat(item.Datetime),
    })),
    "Y-Axis": data["Y-Axis"]?.flatMap((axis) =>
      axis?.name?.split(" and ")?.map((name) => ({ name, type: axis.type }))
    ),
  };
  // Calculate min and max values for each key
  const minMaxValues = {};
  convertedData["Y-Axis"]?.forEach((axis) => {
    const key = axis.name;
    minMaxValues[key] = {
      min: Math.min(...convertedData.data?.map((item) => item[key] - 1.5)),
      max: Math.max(...convertedData.data?.map((item) => item[key] + 0.5)),
    };
  });
  const yAxisWidths = convertedData["Y-Axis"]?.map((axis) => {
    const key = axis.name;
    const maxLength = Math.max(
      ...convertedData.data?.map((item) => String(item[key]).length)
    );
    // Adjust the multiplier and other factors as needed to fit your layout
    return ischart ? maxLength * 8 : maxLength * 12; // Adjust the multiplier as needed
  });

  return (
    <ResponsiveContainer
      width={dimension ? dimension.w * 160 : "100%"}
      height={dimension ? dimension.h * 70 : 440}
    >
      <ComposedChart
        data={convertedData.data}
        margin={{
          top: 35,
          right: 20,
          bottom: ischart ? 40 : 50,
          left: ischart ? 10 : 20,
        }}
      >
        <CartesianGrid stroke="#f5f5f5" />
        <XAxis
          dataKey="Datetime"
          // scale="band"
          tick={{ fontSize: ischart ? 10 : 12, fontWeight: 600 }}
          angle={-45}
          textAnchor="end"
          interval={2}
        />
        <Tooltip />
        {!ischart ? (
          <Legend
            verticalAlign="top"
            layout="horizontal"
            wrapperStyle={legendWrapperStyle}
          />
        ) : null}

        {convertedData["Y-Axis"]?.map((axis, index) => {
          if (axis.type === "Line Graph") {
            return (
              <Line
                type="natural"
                dot={{ strokeWidth: 1.5, r: 3 }}
                strokeWidth={3.5}
                key={index}
                dataKey={axis.name}
                stroke={colors[index]}
                yAxisId={index}
              />
            );
          } else if (axis.type === "Bar Graph") {
            return (
              <Bar
                barSize={20}
                key={axis.name}
                dataKey={axis.name}
                fill={colors[index]}
                yAxisId={index}
                barGap={0}
                barCategoryGap={0}
              />
            );
          }
        })}

        {convertedData["Y-Axis"]?.map((axis, index) => {
          const { min, max } = minMaxValues[axis.name];
          const domainPadding = Math.max((max - min) / 10, 1); // Ensure at least 10 ticks
          return (
            <YAxis
              key={axis.name}
              yAxisId={index}
              tick={{ fontSize: ischart ? 10 : 12, fontWeight: 600 }}
              orientation={index === 0 ? "left" : "right"}
              //  domain={[min, max]} // Extend the upper limit for padding
              tickCount={10} // Ensure at least 10 ticks
              stroke={colors[index]}
              width={yAxisWidths[index]}
            />
          );
        })}
      </ComposedChart>
    </ResponsiveContainer>
  );
};

export default MultiAxisChart;
