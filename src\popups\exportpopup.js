import React, { useState } from "react";
import Dialog from "@mui/material/Dialog";
import Button from "../components/Button/OutlinedButton";
import CancelButton from "../components/Button/Button";
import { CloseIcon } from "../icons";
import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";
import theme from "../tailwind-theme";
import { default_file_type } from "../common/constants";

function ExportPopup({ show, onHide, onConfirm, identity, exportPermissions }) {
  const [selectedOptions, setSelectedOptions] = useState(
    identity === "Logs"
      ? "PDF"
      : identity === "CDR export"
      ? "CSV"
      : default_file_type
  );

  const handleOptionChange = (event) => {
    setSelectedOptions(event.target.value);
  };
  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 278,
          maxHeight: 232,
        },
      }}
      maxWidth="xs"
      open={show}
      onClose={onHide}
      className="p-6 font-sans"
    >
      <div className="mx-5">
        <div className=" mt-4 text-black text-base font-medium flex items-center justify-between">
          {"Export Reports"}
          <CloseIcon
            onClick={() => {
              onHide();
            }}
            className=" w-2.5 h-2.5"
          />
        </div>

        <div className="mt-2  mb-3 border-b border-panelBorder" />
        <div style={{ display: "flex", flexDirection: "column", padding: 0 }}>
          {(identity !== "Reports" || exportPermissions?.csv) && (
            <FormControlLabel
              control={
                <Checkbox
                  value="CSV"
                  checked={selectedOptions.includes("CSV")}
                  onChange={handleOptionChange}
                  sx={{
                    "& .MuiSvgIcon-root": {
                      fontSize: 14,
                      color: theme.textColor.titleColor,
                    },
                  }}
                />
              }
              label={<span className="text-xs font-medium">CSV</span>}
            />
          )}

          {identity !== "CDR export" && (
            <>
              {(identity !== "Reports" || exportPermissions?.excel) && (
                <FormControlLabel
                  control={
                    <Checkbox
                      value="EXCEL"
                      checked={selectedOptions.includes("EXCEL")}
                      onChange={handleOptionChange}
                      sx={{
                        "& .MuiSvgIcon-root": {
                          fontSize: 14,
                          color: theme.textColor.titleColor,
                        },
                      }}
                    />
                  }
                  label={<span className="text-xs font-medium">EXCEL</span>}
                />
              )}
              {(identity !== "Reports" || exportPermissions?.pdf) && (
                <FormControlLabel
                  control={
                    <Checkbox
                      value="PDF"
                      checked={selectedOptions.includes("PDF")}
                      onChange={handleOptionChange}
                      sx={{
                        "& .MuiSvgIcon-root": {
                          fontSize: 14,
                          color: theme.textColor.titleColor,
                        },
                      }}
                    />
                  }
                  label={<span className="text-xs font-medium">PDF</span>}
                />
              )}
            </>
          )}
        </div>
        <div>
          <div className="text-center mt-5 gap-5 mb-4 flex">
            <CancelButton
              onClick={onHide}
              label={"Cancel"}
              buttonClassName="w-[100px] h-9 text-xs"
            />
            <Button
              onClick={() => onConfirm(selectedOptions)}
              label={"Export"}
              buttonClassName="w-[100px] h-9 text-xs"
            />
          </div>
        </div>
      </div>
    </Dialog>
  );
}

export default ExportPopup;
