import React, { useContext, Suspense, useEffect, lazy } from "react";
import {
  Routes as Switch,
  Route,
  Navigate as Redirect,
  useLocation,
} from "react-router-dom";
import routes from "../routes";

import Sidebar from "../components/Sidebar";
import Header from "../components/Header";
import Main from "../containers/Main";
import ThemedSuspense from "../components/ThemedSuspense";
import { SidebarContext } from "../context/SidebarContext";

const Page404 = lazy(() => import("../pages/404"));
function Layout() {
  const { isSidebarOpen, closeSidebar } = useContext(SidebarContext);
  let location = useLocation();

  useEffect(() => {
    closeSidebar();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);

  return (
    <div className={`h-screen overflow-x-hidden overflow-y-auto bg-bgPrimary`}>
      <div className="h-full flex flex-col overflow-auto ">
        <div className="z-9999">
          <Header />
        </div>
        <div className="h-full flex overflow-x-hidden overflow-y-auto">
          <div className="shrink-0">
            <div className="flex h-full overflow-hidden">
              <Sidebar />
            </div>
          </div>
          {/* <div>
            <Sidebar />
          </div> */}
          <div className="flex flex-col flex-1 w-full overflow-x-hidden">
            <Main>
              <Suspense fallback={<ThemedSuspense />}>
                <Switch>
                  {routes.map((route, i) => {
                    return route.component ? (
                      <Route
                        key={i}
                        path={`${route.path}`}
                        element={<route.component />}
                      />
                    ) : null;
                  })}
                  {/* <Redirect exact from="/app" to="/app/dashboard" /> */}
                  <Route component={Page404} />
                </Switch>
              </Suspense>
            </Main>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Layout;
