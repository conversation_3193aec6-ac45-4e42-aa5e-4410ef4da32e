export const SelectedFiltersDisplay = ({ conditions }) => {
  return (
    <>
      {conditions?.map((condition, index) => {
        const words = Object.values(condition);
        const lastIndex = words.length - 1;
        let lastWord = "";
        if (index !== conditions?.length - 1) {
          words[lastIndex] = `{${words[lastIndex]}}`;
        } else {
          lastWord = words.pop();
        }
        const isEmpty = conditions?.every((condition) => {
          return Object?.values(condition)?.every((value) => value === "");
        });
        return (
          <div key={index} className="mb-1">
            <span className={`rounded-md p-1 text-xs ml-1`}>
              {isEmpty ? "No Filters Selected" : words?.join(" ")}
            </span>
          </div>
        );
      })}
    </>
  );
};
