import { useContext, useEffect, useState } from "react";
import { Formik, Form, ErrorMessage, Field } from "formik";
import TextFieldWrapper from "../components/FormsUI/TextField";
import Select from "../components/FormsUI/Select";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import BackButton from "../components/Button/Button";
import Button from "../components/Button/OutlinedButton";
import { timePeriodOptions } from "../common/constants";
import { useMutation, useQuery } from "react-query";
import { alertServices } from "../services/alert-api";
import { useNavigate } from "react-router-dom";
import ChipInput from "../components/Chip/ChipInput";
import GroupEmailComponent from "../common/groupEmail";
import { RadioGroup, Radio } from "@mui/material";
import FormControlLabel from "@mui/material/FormControlLabel";
import { DataContext } from "../context/DataContext";
import "react-toastify/dist/ReactToastify.css";
import { toast, ToastContainer } from "react-toastify";
import { createAlert, updateAlert } from "../services/alert-api";
import SuccessDialog from "../popups/SuccessDialog";
import ErrorDialog from "../popups/ErrorDialog";
import * as Yup from "yup";

const AddAlertFormFour = ({ isEdit, alertId, editDetail, isCopy }) => {
  const {
    handleNextClick,
    setFormData,
    formData,
    handleNextClickStep,
    handlePrevClick,
    handlePrevStep,
  } = useContext(multiStepFormContext);
  const [groups, setGroups] = useState([]);
  const [selectedName, setSelectedName] = useState(
    editDetail?.contact_point?.groupsEmails?.filter(
      (group) => !group.includes("@")
    ) || []
  );
  const [groupMembers, setGroupMembers] = useState([]);
  const [groupType, setGroupType] = useState(
    editDetail?.contact_point?.groupsEmails?.length > 0 ? "Group" : "Individual"
  );
  const [suceessDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [errorDialog, setErrorDialog] = useState(false);
  const [newChipState, setNewChipState] = useState(
    editDetail?.contact_point?.groupsEmails?.filter((group) =>
      group.includes("@")
    ) || []
  );

  const navigate = useNavigate();
  const { mutate: createAlertAPI, isLoading: creationLoading } = useMutation(
    alertServices.createAlert
  );
  const { mutate: updateAlertAPI, isLoading: upadteLoading } = useMutation(
    alertServices.updateAlert
  );
  const { setAlertDetails } = useContext(DataContext);
  const { data: groupDetails } = useQuery(
    ["groupList", 1, 1000],
    alertServices.getAll,
    {
      onSuccess: ({ data }) => {
        const groupDetails = data?.data?.map((item) => ({
          label: item.name,
          value: item.name,
          members: item.members,
          subGroups: item.subGroups,
        }));
        setGroups(groupDetails);
      },
    }
  );
  const handleChipRemove = (index, formikBag) => {
    const newChips = [...formikBag.values.chips];
    newChips.splice(index, 1);
    formikBag.setFieldValue("chips", newChips);
  };

  const handleChipAdd = (chip, formikBag) => {
    formikBag.setFieldValue("chips", [...formikBag.values.chips, chip]);
  };

  const handleSubmit = (values, newData) => {
    let reqData = {};

    const specialKeys = ["Customer", "Supplier", "Destination"];
    const operatorMapping = {
      "Less than equal to": "<=",
      "Greater than equal to": ">=",
      "Less than": "<",
      "More than": ">",
    };

    Object.keys(formData).forEach((key) => {
      const value = formData[key];
      if (
        key === "groupName" ||
        key === "newChips" ||
        key === "filterType" ||
        key === "timeInterval" ||
        key === "timePeriod" ||
        key === "chips" ||
        key === "conditions" ||
        key === "minimumSubmissionCount" ||
        key === "groupType"
      )
        return;

      if (specialKeys.includes(key)) {
        if (key === "Customer") reqData.customers = value;
        else if (key === "Supplier") reqData.suppliers = value || [];
        else if (key === "Destination") reqData.destinations = value || [];
      } else {
        if (value !== undefined && value !== null) {
          reqData[key] = value;
        }
      }
    });

    if (values?.chips?.length > 0) reqData.individuals = values?.chips;
    if (selectedName || newChipState)
      reqData.groups = [...(selectedName || []), ...(newChipState || [])];
    if (formData?.timePeriod) reqData.timerange = formData?.timePeriod;
    if (formData?.minimumSubmissionCount)
      reqData.minSubmissionCount = formData?.minimumSubmissionCount;

    if (formData?.timeInterval)
      reqData.evaluationTimeframe = formData?.timeInterval;
    if (formData?.timeInterval) reqData.runEvery = formData?.timeInterval;

    if (Array.isArray(formData.conditions)) {
      const newConditions = formData.conditions.map((condition) => {
        const type1 = condition.type1.toLowerCase().replace(/ /g, "_");
        const operator = operatorMapping[condition.type2] || condition.type2;
        const type3 = condition.type3;
        return `${type1} ${operator} ${type3}`;
      });

      reqData.filters = newConditions;
    }

    if (isEdit) {
      updateAlertAPI(
        {
          alertId,
          reqData,
        },
        {
          onSuccess: (resp) => {
            setSuccessDialog(true);
            setMessage("Alert updated successfully");
          },
          onError: (error) => {
            setMessage(error?.response?.data?.message);
            setErrorDialog(true);
          },
        }
      );
    } else {
      createAlertAPI(
        {
          reqData,
        },
        {
          onSuccess: (resp) => {
            setSuccessDialog(true);
            setMessage("Alert created successfully");
          },
          onError: (error) => {
            setMessage(error?.response?.data?.message);
            setErrorDialog(true);
          },
        }
      );
    }
  };
  const initialValues = {
    groupType: groupType,
    ...(groupType === "Individual"
      ? {
          chips:
            formData?.chips || editDetail?.contact_point?.individuals || [],
          groupName: "",
          newChips: "",
        }
      : {
          groupName: selectedName || "", // Values without @
          newChips:
            editDetail?.contact_point?.groupsEmails?.filter((group) =>
              group.includes("@")
            ) ||
            newChipState ||
            "", // Values with @
          chips: [],
        }),
  };

  const validationSchema = Yup.object().shape({
    chips: Yup.array().when("groupType", {
      is: "Individual",
      then: (schema) =>
        schema
          .min(1, "Please enter at least one Email ID")
          .required("Please enter at least one Email ID"),
      otherwise: (schema) => schema.notRequired(),
    }),
    groupName: Yup.array().when("groupType", {
      is: "Group",
      then: (schema) =>
        schema
          .min(1, "Please select at least one group")
          .required("Please select at least one group"),
      otherwise: (schema) => schema.notRequired(),
    }),
  });
  return (
    <div>
      <Formik
        enableReinitialize
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          const keys = Object.keys(values);
          setAlertDetails((prevDetails) => {
            const newDetails = [...prevDetails];
            newDetails[3] = keys; // Store keys for step 4
            return newDetails;
          });
          // handleNextClickStep();
          let newData = Object.assign(formData, values);
          handleSubmit(values, newData);

          setFormData(newData);
        }}
      >
        {({ setFieldValue, resetForm, errors, touched, values }) => (
          <Form>
            <div className="flex flex-col   ">
              <div className="flex flex-row mx-28 justify-between">
                <div className="flex flex-col">
                  {" "}
                  {/*console.log("")*/}
                  {/* <div className=" py-4">Send Email</div>{" "} */}
                  <InputLabel
                    label={"Send Email"}
                    isMandatory={true}
                    labelClassName={"mb-3"}
                  />
                  <div className="px-1">
                    <RadioGroup
                      name="groupType"
                      row
                      value={groupType}
                      onChange={(e) => {
                        setGroupType(e.target.value);
                        if (e.target.value === "Individual") {
                          setFieldValue("groupName", []);
                          setFieldValue("newChips", []);
                          setSelectedName([]);
                          setNewChipState([]);
                        } else if (e.target.value === "Group") {
                          setFieldValue("chips", []);
                        }
                      }}
                    >
                      <FormControlLabel
                        value="Individual"
                        //  disabled={edit}
                        control={
                          <Radio
                            sx={{
                              "& .MuiSvgIcon-root": {
                                fontSize: "16px",
                              },
                              "&.Mui-checked .MuiSvgIcon-root": {
                                color: "black",
                              },
                            }}
                          />
                        }
                        label={
                          <span className="text-headingColor text-sm">
                            Individuals
                          </span>
                        }
                      />
                      <FormControlLabel
                        value="Group"
                        // disabled={edit}
                        control={
                          <Radio
                            sx={{
                              "& .MuiSvgIcon-root": {
                                fontSize: "16px",
                              },
                              "&.Mui-checked .MuiSvgIcon-root": {
                                color: "black",
                              },
                            }}
                          />
                        }
                        label={
                          <span className="text-headingColor text-sm">
                            Groups
                          </span>
                        }
                      />
                    </RadioGroup>
                  </div>
                </div>
                {groupType === "Group" ? (
                  <div className="w-1/2 md:w-[550px]">
                    <InputLabel
                      label={"Groups"}
                      isMandatory={true}
                      labelClassName={"mb-3"}
                    />
                    <Select
                      name="groupName"
                      options={groups}
                      placeholder="Select Groups"
                      onChange={(selectedOptions) => {
                        const selectedValues = selectedOptions?.map(
                          (option) => option.value
                        );
                        setFieldValue("groupName", selectedValues);
                        setSelectedName(selectedValues);
                      }}
                      isMulti
                      isSearchable={true}
                    />
                  </div>
                ) : (
                  <div className="w-[550px] ">
                    <div className="flex mb-2">
                      <InputLabel label={"Individuals"} isMandatory={true} />
                      <InputLabel
                        label={"(Press enter to add Email ID)"}
                        labelClassName={"text-xs ml-1 mt-0.5"}
                      />
                    </div>

                    <Field
                      id="chips"
                      name="chips"
                      render={(fieldProps) => (
                        <ChipInput
                          chips={fieldProps.field.value}
                          onChipAdd={(chip) =>
                            handleChipAdd(chip, fieldProps.form)
                          }
                          onChipRemove={(index) =>
                            handleChipRemove(index, fieldProps.form)
                          }
                          isEdit={true}
                          placeholder={"Enter Email ID"}
                        />
                      )}
                    />
                    <p className="text-errorColor text-xs my-1">
                      <ErrorMessage name="chips" />
                    </p>
                  </div>
                )}{" "}
              </div>
              {(groupType === "Group" && selectedName?.length > 0) ||
              (newChipState && newChipState?.length > 0) ? (
                <GroupEmailComponent
                  groups={groups}
                  selectedName={selectedName}
                  setFieldValue={setFieldValue}
                  setGroupMembers={setGroupMembers}
                  groupMembers={groupMembers}
                  newChipState={newChipState}
                  setNewChipState={setNewChipState}
                />
              ) : null}
            </div>
            <div className="flex-grow flex justify-end items-center mx-20 mt-20 mb-20 gap-4">
              <BackButton
                label={"Back"}
                buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded"
                onClick={() => {
                  handlePrevClick();
                  handlePrevStep();
                }}
              ></BackButton>
              <Button
                // block="true"
                type="submit"
                label="Save"
                // value="submit"
                buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded "
                //loading={creationLoading || upadteLoading}
              ></Button>
            </div>
          </Form>
        )}
      </Formik>
      <SuccessDialog
        show={suceessDialog}
        onHide={() => {
          setSuccessDialog(false);
          navigate("/app/alerts");
        }}
        message={message}
      />
      <ErrorDialog
        show={errorDialog}
        onHide={() => setErrorDialog(false)}
        message={message}
      />
    </div>
  );
};
export default AddAlertFormFour;
