export const fileDownload = (res, filename = "download", downloadType) => {
  const isBlob = res?.data instanceof Blob;
  if (!isBlob) {
    return console.error(`Failed to download res is not Blob`);
  }

  // Mapping of download types to file extensions
  const fileExtensions = {
    pdf: ".pdf",
    csv: ".csv",
    excel: ".xlsx",
  };
  const extension = fileExtensions[downloadType.toLowerCase()];
  if (!extension) return console.error(`Invalid download type`);

  // Create an object URL from the binary data received in the response
  const url = URL.createObjectURL(res.data);
  const link = document.createElement("a");
  link.href = url;

  // Trigger download by simulating a click on the link
  link.download = filename + extension;
  link.click();

  // Return null to avoid returning the normal data when downloading
  return null;
};
