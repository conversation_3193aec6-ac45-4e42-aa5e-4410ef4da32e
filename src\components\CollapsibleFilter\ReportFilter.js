import React, { useContext, useRef, useState } from "react";
import { Formik, Form, Field } from "formik";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CustomDropDown from "../../components/Dropdown/ReportsDropdownList";
import InputLabel from "../FormsUI/InputLabel/InputLabel";
import Button from "../../components/Button/Button";
import OutlinedButton from "../../components/Button/OutlinedButton";
import TextField from "../FormsUI/TextField";
import { MetaDataContext } from "../../context/MetaDataContext";
import { useQuery } from "react-query";
import { reportService } from "../../services/staticreport.service";
import { DataContext } from "../../context/DataContext";
import * as Yup from "yup";
import { filterLabelMap } from "../../common/constants";

export default function FilterComponent({
  reportName,
  setFilters,
  openFilterDialog,
  closeFilterDialog,
  filterData,
  setLabelData,
  visualizationType,
}) {
  const formikRef = useRef(null);
  const { setBilateralData } = useContext(DataContext);
  const [availableFilters, setAvailableFilters] = useState([]);
  const [otherFilter, setOtherFilter] = useState([]);
  const {
    customers,
    customerBind,
    suppliers,
    supplierBind,
    destinationNameList,
    destinationCountryList,
    lcrDataList,
    cdrStatus,
    customerProtocol,
    supplierProtocol,
    supplierBillingLogic,
    customerBillingLogic,
    specLCRDataList,
    customerInterfaceType,
    supplierInterfaceType,
    sourcePrime,
    destinationPrime,
    customerAirtelKamList,
    supplierAirtelKamList,
  } = useContext(MetaDataContext);

  const initialValues = {
    customer_name: [],
    customer_bind: [],
    src_prime: [],
    destination: [],
    dest_prime: [],
    supplier: [],
    supplier_name: [],
    supplier_bind: [],
    destination_operator_name: [],
    destination_country_name: [],
    customer_interface_type: [],
    supplier_interface_type: [],
    customer_billing_logic: [],
    supplier_billing_logic: [],
    traffic_type_customer: [],
    traffic_type_supplier: [],
    destination_mcc_final: filterData?.destination_mcc_final ?? "",
    destination_mnc_final: filterData?.destination_mnc_final ?? "",
    lcr_name: [],
    spec_lcr: [],
    status: [],
    customer_kam: [],
    supplier_kam: [],
    roamingDirectStatus: filterData
      ? filterData?.only_roaming
        ? "only_roaming"
        : filterData?.only_direct
        ? "only_direct"
        : filterData?.both
        ? "both"
        : ""
      : "",
    negative_report: filterData?.negative_report || false,
    bilateral: filterData?.bilateral || false,
  };
  const customerOptions = [
    { value: " ", label: "Empty String" },
    ...(customerAirtelKamList || []),
  ];
  const supplierOptions = [
    { value: " ", label: "Empty String" },
    ...(supplierAirtelKamList || []),
  ];

  const handleSubmit = (values) => {
    const allFilters = [...availableFilters, ...otherFilter];

    const normalizedValues = {
      ...values,
      ...(values.destination_mcc_final && {
        destination_mcc_final: parseInt(values.destination_mcc_final, 10),
      }),
      ...(values.destination_mnc_final && {
        destination_mnc_final: parseInt(values.destination_mnc_final, 10),
      }),
    };

    const filteredValues = Object.keys(normalizedValues)
      .filter((key) => {
        const value = normalizedValues[key];
        return (
          allFilters.includes(key) &&
          value !== null &&
          value !== undefined &&
          value !== "" &&
          value !== false &&
          (!Array.isArray(value) || value.length > 0)
        );
      })
      .reduce((obj, key) => {
        obj[key] = normalizedValues[key];
        return obj;
      }, {});

    const appliedLabels = [];
    if (
      otherFilter.includes("only_roaming") ||
      otherFilter.includes("only_direct") ||
      otherFilter.includes("both")
    ) {
      const statusList = normalizedValues.roamingDirectStatus || [];
      appliedLabels.push(filterLabelMap["roamingDirectStatus"]);

      if (statusList.includes("only_roaming")) {
        filteredValues.only_roaming = true;
      }
      if (statusList.includes("only_direct")) {
        filteredValues.only_direct = true;
      }
      if (statusList.includes("both")) {
        filteredValues.both = true;
      }
    }

    appliedLabels.push(
      ...Object.keys(filteredValues)
        .filter((key) => filterLabelMap[key])
        .map((key) => filterLabelMap[key])
    );

    setFilters(filteredValues);
    setLabelData(appliedLabels);
    closeFilterDialog();
  };

  const shouldShowFilter = (filterName) => {
    return availableFilters.includes(filterName);
  };

  useQuery(
    ["reportFilter", reportName, visualizationType],
    reportService.getReportFilter,
    {
      refetchOnWindowFocus: false,
      onSuccess: ({ data }) => {
        setAvailableFilters(data?.filters || []);
        setOtherFilter(data?.otherFilters || []);
      },
      onError: () => {},
    }
  );

  const validation = Yup.object().shape({
    destination_mcc_final: Yup.number()
      .typeError("Destination MCC must be a number")
      .integer("Destination MCC must be an integer")
      .max(999, "DestinationMCC must be a 3-digit code")
      .notOneOf([0], "Destination MCC cannot be 000"),
    destination_mnc_final: Yup.number()
      .typeError("Destination MNC must be a number")
      .integer("Destination MNC must be an integer")
      .max(999, "Destination MNC must be between 1 and 3 digits"),
  });

  return (
    <div className="w-full mx-auto mt-5">
      {/* MUI Dialog */}
      <Dialog
        open={openFilterDialog}
        onClose={() => {
          closeFilterDialog();
        }}
        fullWidth
        maxWidth="md"
        aria-labelledby="filter-dialog-title"
        sx={{
          "& .MuiDialog-container": {
            "& .MuiPaper-root": {
              width: "100%",
              maxWidth: "1000px",
              minHeight: "350px",
              margin: 0,
            },
          },
        }}
      >
        <DialogTitle
          id="filter-dialog-title"
          sx={{ m: 0, p: 1, fontSize: "14px" }}
        >
          Filter Options
          <IconButton
            aria-label="close"
            onClick={() => {
              closeFilterDialog();
            }}
            sx={{
              position: "absolute",
              right: 8,
              top: 4,
            }}
          >
            <CloseIcon className="w-4 h-4" />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers>
          <Formik
            initialValues={initialValues}
            onSubmit={handleSubmit}
            innerRef={formikRef}
            enableReinitialize={true}
            validationSchema={validation}
          >
            {({ values, setFieldValue }) => (
              <Form>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-2">
                  {/* Customer Name */}
                  {shouldShowFilter("customer_name") && (
                    <div>
                      <InputLabel label={"Customer Name"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customers || []}
                        btnName={"Select Customer Name"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("customer_name", selectedDetail);
                        }}
                        value={values.customer_name}
                        defaultSelectedData={filterData?.customer_name || []}
                      />
                    </div>
                  )}

                  {/* Customer Bind */}
                  {shouldShowFilter("customer_bind") && (
                    <div>
                      <InputLabel label={"Customer Bind"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customerBind || []}
                        btnName={"Select Customer Bind"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("customer_bind", selectedDetail);
                        }}
                        value={values.customer_bind}
                        defaultSelectedData={filterData?.customer_bind || []}
                      />
                    </div>
                  )}

                  {/* Source Prime */}
                  {shouldShowFilter("src_prime") && (
                    <div>
                      <InputLabel label={"Source Prime"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={sourcePrime || []}
                        btnName={"Select Source Prime"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("src_prime", selectedDetail);
                        }}
                        value={values.src_prime}
                        defaultSelectedData={filterData?.src_prime || []}
                      />
                    </div>
                  )}

                  {/* Destination Prime */}
                  {shouldShowFilter("dest_prime") && (
                    <div>
                      <InputLabel label={"Destination Prime"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={destinationPrime || []}
                        btnName={"Select Destination Prime"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("dest_prime", selectedDetail);
                        }}
                        value={values.dest_prime}
                        defaultSelectedData={filterData?.dest_prime || []}
                      />
                    </div>
                  )}

                  {/* Supplier Name */}
                  {shouldShowFilter("supplier") && (
                    <div>
                      <InputLabel label={"Supplier Name"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={suppliers || []}
                        btnName={"Select Supplier Name"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("supplier", selectedDetail);
                        }}
                        value={values.supplier}
                        defaultSelectedData={filterData?.supplier || []}
                      />
                    </div>
                  )}

                  {/* Supplier Bind */}
                  {shouldShowFilter("supplier_bind") && (
                    <div>
                      <InputLabel label={"Supplier Bind"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={supplierBind || []}
                        btnName={"Select Supplier Bind"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("supplier_bind", selectedDetail);
                        }}
                        value={values.supplier_bind}
                        defaultSelectedData={filterData?.supplier_bind || []}
                      />
                    </div>
                  )}

                  {/* Destination Operator */}
                  {shouldShowFilter("destination_operator_name") && (
                    <div>
                      <InputLabel label={"Destination Operator"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={destinationNameList || []}
                        btnName={"Select Destination Operator"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "destination_operator_name",
                            selectedDetail
                          );
                        }}
                        value={values.destination_operator_name}
                        defaultSelectedData={
                          filterData?.destination_operator_name || []
                        }
                      />
                    </div>
                  )}

                  {/* Destination Operator */}
                  {shouldShowFilter("destination") && (
                    <div>
                      <InputLabel label={"Destination Operator"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={destinationNameList || []}
                        btnName={"Select Destination Operator"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("destination", selectedDetail);
                        }}
                        value={values.destination}
                        defaultSelectedData={filterData?.destination || []}
                      />
                    </div>
                  )}

                  {/* Destination Country */}
                  {shouldShowFilter("destination_country_name") && (
                    <div>
                      <InputLabel label={"Destination Country"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={destinationCountryList || []}
                        btnName={"Select Destination Country"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "destination_country_name",
                            selectedDetail
                          );
                        }}
                        value={values.destination_country_name}
                        defaultSelectedData={
                          filterData?.destination_country_name || []
                        }
                      />
                    </div>
                  )}

                  {/* Customer Interface Type */}
                  {shouldShowFilter("customer_interface_type") && (
                    <div>
                      <InputLabel label={"Customer Interface"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customerInterfaceType || []}
                        btnName={"Select Customer Interface"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "customer_interface_type",
                            selectedDetail
                          );
                        }}
                        value={values.customer_interface_type}
                        defaultSelectedData={
                          filterData?.customer_interface_type || []
                        }
                      />
                    </div>
                  )}

                  {/* Supplier Interface Type */}
                  {shouldShowFilter("supplier_interface_type") && (
                    <div>
                      <InputLabel label={"Supplier Interface"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={supplierInterfaceType || []}
                        btnName={"Select Supplier Interface"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "supplier_interface_type",
                            selectedDetail
                          );
                        }}
                        value={values.supplier_interface_type}
                        defaultSelectedData={
                          filterData?.supplier_interface_type || []
                        }
                      />
                    </div>
                  )}

                  {/* Customer Billing Logic */}
                  {shouldShowFilter("customer_billing_logic") && (
                    <div>
                      <InputLabel label={"Customer Billing Logic"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customerBillingLogic || []}
                        btnName={"Select Customer Billing Logic"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "customer_billing_logic",
                            selectedDetail
                          );
                        }}
                        value={values.customer_billing_logic}
                        defaultSelectedData={
                          filterData?.customer_billing_logic || []
                        }
                      />
                    </div>
                  )}

                  {/* Supplier Billing Logic */}
                  {shouldShowFilter("supplier_billing_logic") && (
                    <div>
                      <InputLabel label={"Supplier Billing Logic"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={supplierBillingLogic || []}
                        btnName={"Select Supplier Billing Logic"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "supplier_billing_logic",
                            selectedDetail
                          );
                        }}
                        value={values.supplier_billing_logic}
                        defaultSelectedData={
                          filterData?.supplier_billing_logic || []
                        }
                      />
                    </div>
                  )}

                  {/* Customer Protocol */}
                  {shouldShowFilter("traffic_type_customer") && (
                    <div>
                      <InputLabel label={"Customer Traffic Type"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customerProtocol || []}
                        btnName={"Select Customer Traffic Type"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "traffic_type_customer",
                            selectedDetail
                          );
                        }}
                        value={values.traffic_type_customer}
                        defaultSelectedData={
                          filterData?.traffic_type_customer || []
                        }
                      />
                    </div>
                  )}

                  {/* Supplier Protocol */}
                  {shouldShowFilter("traffic_type_supplier") && (
                    <div>
                      <InputLabel label={"Supplier Traffic Type"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={supplierProtocol || []}
                        btnName={"Select Supplier Traffic Type"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue(
                            "traffic_type_supplier",
                            selectedDetail
                          );
                        }}
                        value={values.traffic_type_supplier}
                        defaultSelectedData={
                          filterData?.traffic_type_supplier || []
                        }
                      />
                    </div>
                  )}

                  {/* Destination MCC */}
                  {shouldShowFilter("destination_mcc_final") && (
                    <div>
                      <InputLabel label={"Destination MCC"} />
                      <TextField
                        name="destination_mcc_final"
                        placeholder={"Enter destination MCC"}
                      />
                    </div>
                  )}

                  {/* Destination MNC */}
                  {shouldShowFilter("destination_mnc_final") && (
                    <div>
                      <InputLabel label={"Destination MNC"} />
                      <TextField
                        name="destination_mnc_final"
                        placeholder={"Enter destination MNC"}
                      />
                    </div>
                  )}

                  {/* LCR Name */}
                  {shouldShowFilter("lcr_name") && (
                    <div>
                      <InputLabel label={"LCR Name"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={lcrDataList || []}
                        btnName={"Select LCR Name"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("lcr_name", selectedDetail);
                        }}
                        value={values.lcr_name}
                        defaultSelectedData={filterData?.lcr_name || []}
                      />
                    </div>
                  )}

                  {/* Spec LCR */}
                  {shouldShowFilter("spec_lcr") && (
                    <div>
                      <InputLabel label={"Spec LCR"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={specLCRDataList || []}
                        btnName={"Select Spec LCR"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("spec_lcr", selectedDetail);
                        }}
                        value={values.spec_lcr}
                        defaultSelectedData={filterData?.spec_lcr || []}
                      />
                    </div>
                  )}

                  {/* Status */}
                  {shouldShowFilter("status") && (
                    <div>
                      <InputLabel label={"Status"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={cdrStatus || []}
                        btnName={"Select Status"}
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("status", selectedDetail);
                        }}
                        value={values.status}
                        defaultSelectedData={filterData?.status || []}
                      />
                    </div>
                  )}
                  {shouldShowFilter("customer_kam") && (
                    <div>
                      <InputLabel label={"Customer KAM"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={customerOptions || []}
                        btnName="Select Customer KAM"
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("customer_kam", selectedDetail);
                        }}
                        value={values.customer_kam}
                        defaultSelectedData={filterData?.customer_kam || []}
                      />
                    </div>
                  )}
                  {shouldShowFilter("supplier_kam") && (
                    <div>
                      <InputLabel label={"Supplier KAM"} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={supplierOptions || []}
                        btnName="Select Supplier KAM"
                        onSelectionChange={(selectedDetail) => {
                          setFieldValue("supplier_kam", selectedDetail);
                        }}
                        value={values.supplier_kam}
                        defaultSelectedData={filterData?.supplier_kam || []}
                      />
                    </div>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4  mt-5">
                  {(otherFilter.includes("only_roaming") ||
                    otherFilter.includes("only_direct") ||
                    otherFilter.includes("both")) && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Roaming and Direct Status:
                      </label>
                      <div className="flex flex-col gap-2 mt-3">
                        {otherFilter.includes("only_roaming") && (
                          <label className="inline-flex items-center">
                            <Field
                              type="radio"
                              name="roamingDirectStatus"
                              value="only_roaming"
                              className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Only Roaming
                            </span>
                          </label>
                        )}
                        {otherFilter.includes("only_direct") && (
                          <label className="inline-flex items-center">
                            <Field
                              type="radio"
                              name="roamingDirectStatus"
                              value="only_direct"
                              className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Only Direct
                            </span>
                          </label>
                        )}
                        {otherFilter.includes("both") && (
                          <label className="inline-flex items-center">
                            <Field
                              type="radio"
                              name="roamingDirectStatus"
                              value="both"
                              className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Both
                            </span>
                          </label>
                        )}
                      </div>
                    </div>
                  )}
                  {(otherFilter.includes("bilateral") ||
                    otherFilter.includes("negative_report")) && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Others:
                      </label>
                      <div className="flex flex-col gap-2 mt-2">
                        {otherFilter.includes("negative_report") && (
                          <label className="inline-flex items-center">
                            <Field
                              type="checkbox"
                              name="negative_report"
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Negative Report Required
                            </span>
                          </label>
                        )}
                        {otherFilter.includes("bilateral") && (
                          <Field name="bilateral">
                            {({ field, form }) => (
                              <label className="inline-flex items-center">
                                <input
                                  type="checkbox"
                                  {...field}
                                  checked={field.value}
                                  onChange={(e) => {
                                    const checked = e.target.checked;
                                    form.setFieldValue("bilateral", checked);
                                    setBilateralData(checked);

                                    setFieldValue(
                                      "destination_operator_name",
                                      []
                                    );
                                    setFieldValue(
                                      "destination_country_name",
                                      []
                                    );
                                    setFieldValue("destination", []);
                                  }}
                                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                />
                                <span className="ml-2 text-sm text-gray-700">
                                  Include Bilateral
                                </span>
                              </label>
                            )}
                          </Field>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </Form>
            )}
          </Formik>
        </DialogContent>

        <DialogActions>
          <div className="flex gap-4 ">
            <Button
              label="Clear"
              type="button"
              buttonClassName={"w-24"}
              onClick={() => {
                if (formikRef.current) {
                  formikRef.current.resetForm();
                  setFilters([]);
                  setBilateralData(false);
                }
              }}
            />
            <OutlinedButton
              label="Apply"
              type="button"
              buttonClassName={"w-24"}
              onClick={() => {
                if (formikRef.current) {
                  formikRef.current.handleSubmit();
                }
              }}
            />
          </div>
        </DialogActions>
      </Dialog>
    </div>
  );
}
