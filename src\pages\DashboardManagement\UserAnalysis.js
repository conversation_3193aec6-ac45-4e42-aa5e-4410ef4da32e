import React, { useState, useEffect } from "react";
import { useQuery, useMutation } from "react-query";
import { useNavigate, useLocation } from "react-router-dom";
import { getAll, getDashboardById } from "../../services/dashboard-api";
import { getAllCards, getCardsById } from "../../services/cards-preview-api";
import AvailableItemsAccordion from "../../components/Accordion/AvailableItemsAccordion";
import { previewPanel } from "../../services/panels-api";
import ResponsiveLayout from "./ResponsiveLayout";
import { getAll as getAllPanels } from "../../services/panels-api";
import CardData from "./CardData";
import { getId } from "../../services/panels-api";
import PanelVisualization from "./PanelVisualization";
import { CircularProgress } from "@mui/material";
import bgImage from "../../assets/img/useranalysis.png";
import { getAllUser } from "../../services/cards-preview-api";
import BreadcrumbNavigation from "../../components/BreadCrumps/BreadCrump";
import dayjs from "dayjs";
import { CssTooltip } from "../../components/StyledComponent";
import { InfoIcon } from "../../icons";
import { ConditionDisplay } from "../../common/constants";
import { convertToUTC, handleDateFormat } from "../../common/commonFunctions";
import customParseFormat from "dayjs/plugin/customParseFormat";
import ResponsiveLayoutCard from "./ResponsiveLayoutCard";

function UserAnalysis() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedName, setSelectedName] = useState("");
  const [userId, setUserId] = useState("");
  const [searchClicked, setSearchClicked] = useState(false);
  const [dashboardDetails, setDashboardDetails] = useState("");
  const [cardDetails, setCardDetails] = useState("");
  const [panelDetails, setPanelDetails] = useState("");
  const [dashboardId, setDashboardId] = useState("");
  const [cardId, setCardId] = useState("");
  const [panelId, setPanelId] = useState("");
  const [cardDroppedData, setCardDroppedData] = useState([]);
  const [previewResponses, setPreviewResponses] = useState([]);
  const [cardById, setCardById] = useState("");
  const [panelById, setPanelById] = useState("");
  const [responseData, setResponseData] = useState([]);
  const [users, setUsers] = useState([]);
  const [date, setDate] = useState({});
  const [previewLoading, setPreviewLoading] = useState(false);
  const colors = ["#EDDF82", "#82C3ED", "#82EDAD", "#ED8282"];

  const navigate = useNavigate();
  const location = useLocation();
  dayjs.extend(customParseFormat);
  useQuery(["userList"], getAllUser, {
    onSuccess: ({ data }) => {
      let newData = data?.data?.map((x) => {
        return {
          id: x.id,
          name: x.name,
          role: x.role.name,
        };
      });
      setUsers(newData);
      //console.log("data", newData);
    },
  });
  const filteredData = users?.filter((userData) =>
    userData.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useQuery(["dashboadList", "", "", "", userId], getAll, {
    enabled: !!userId,
    onSuccess: ({ data }) => {
      setDashboardDetails(data?.data);
    },
  });
  useQuery(["CardDataList", "", "", "", userId], getAllCards, {
    enabled: !!userId,
    onSuccess: ({ data }) => {
      setCardDetails(data?.data);
    },
  });
  useQuery(["PanelList", "", "", "", userId], getAllPanels, {
    enabled: !!userId,
    onSuccess: ({ data }) => {
      setPanelDetails(data?.data);
    },
  });
  const { isLoading: dashboardLoading } = useQuery(
    ["getDashboardById", dashboardId],
    getDashboardById,
    {
      enabled: !!dashboardId,
      onSuccess: ({ data }) => {
        setCardDroppedData(
          data?.cards.map((card) => ({
            reportField: card.cardDetails.reportField,
            value: card.cardDetails.value,
            order: card.order,
          }))
        );
        const panels = data?.panels?.map((panel, i) => {
          let dimension = {
            w: panel.order > 1 ? 3 : 6,
            h: 3,
            x: 0,
            y: 0,
            order: panel.order,
          };

          return { ...panel, dimension };
        });
        setPreviewResponses(panels);
        previewAPICall(panels);
      },
      refetchOnWindowFocus: false,
    }
  );
  const previewAPICall = (previewData) => {
    previewData.forEach((payload, index) => {
      const selectedRange = payload.panelDetails.timePeriod;

      let formattedStart = "";
      let formattedEnd = "";
      const currentDateTime = dayjs();

      if (selectedRange) {
        if (selectedRange.includes("to")) {
          const [startString, endString] = selectedRange.split("to");
          formattedStart = startString;
          formattedEnd = endString;
        } else {
          if (
            selectedRange === "Last Hour" ||
            selectedRange === "Last 6 Hours" ||
            selectedRange === "Last 12 Hours" ||
            selectedRange === "Last 24 Hours"
          ) {
            const hours = {
              "Last Hour": 1,
              "Last 6 Hours": 6,
              "Last 12 Hours": 12,
              "Last 24 Hours": 24,
            };

            const lastXHours = currentDateTime.subtract(
              hours[selectedRange],
              "hour"
            );
            if (selectedRange === "Last Hour") {
              formattedStart = lastXHours.format("YYYY-MM-DD HH:mm:ss");
              formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
            } else {
              formattedStart = lastXHours.format("YYYY-MM-DD HH:00:00");
              formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
            }
          } else if (selectedRange === "Today") {
            formattedStart = currentDateTime
              .startOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
          } else if (selectedRange === "Yesterday") {
            const yesterday = currentDateTime.subtract(1, "day");
            formattedStart = yesterday
              .startOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss");
          } else if (selectedRange === "Last Seven Days") {
            formattedStart = currentDateTime
              .subtract(6, "days")
              .startOf("day")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "Last Week") {
            formattedStart = currentDateTime
              .subtract(1, "week")
              .startOf("week")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .subtract(1, "week")
              .endOf("week")
              .format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "Last 30 Days") {
            formattedStart = currentDateTime
              .subtract(29, "days")
              .startOf("day")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .endOf("day")
              .format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "Last Month") {
            formattedStart = currentDateTime
              .subtract(1, "month")
              .startOf("month")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .subtract(1, "month")
              .endOf("month")
              .format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "This Month") {
            formattedStart = currentDateTime
              .startOf("month")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime
              .endOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
          }
        }
      }

      let reqData = {
        name: payload.panelDetails.name,
        visualizationType: payload.panelDetails.visualizationType,
        filters: [],
        dataColumns: {
          derivedFields: payload.panelDetails.dataColumns?.derivedFields,
        },
        // startDate: convertToUTC(formattedStart),
        // endDate: convertToUTC(formattedEnd),
        startDate: formattedStart,
        endDate: formattedEnd,
      };

      if (payload.panelDetails.visualizationType === "Bar Graph") {
        reqData.dataColumns["X-Axis"] =
          payload.panelDetails.dataColumns?.["X-Axis"];
        reqData.dataColumns.noOfRecords =
          payload.panelDetails.dataColumns.noOfRecords;
      } else {
        reqData.dataColumns.tableFields =
          payload.panelDetails.dataColumns?.tableFields;
      }

      payload.panelDetails.filters.forEach((condition) => {
        reqData.filters.push({
          field: condition.field,
          condition: condition.condition,
          value: condition.value,
          operator: condition.operator,
        });
      });

      previewPanel({ reqData })
        .then(({ data }) => {
          setPreviewResponses((prevResponses) => {
            let newData = prevResponses?.filter(
              ({ dimension }) => dimension.order !== payload.dimension.order
            );

            newData.push({
              data: data.data,
              panelData: {
                ...reqData,
                data:
                  reqData.visualizationType === "MultiAxis Graph"
                    ? data
                    : data?.data,
              },
              dimension: payload.dimension,
              id: payload.panelDetails.id,
              timePeriod: payload.panelDetails.timePeriod,
              count: data?.count,
            });
            return newData;
          });
        })
        .catch(() => {
          setPreviewResponses((prevResponses) => {
            let newData = prevResponses?.filter(
              ({ dimension }) => dimension.order !== payload.dimension.order
            );

            newData.push({
              data: [],
              panelData: {
                ...reqData,
                data: "failed",
              },
              dimension: payload.dimension,
              id: payload.panelDetails.id,
              timePeriod: payload.panelDetails.timePeriod,
            });
            return newData;
          });
        });
    });
  };
  useQuery(["getCardsById", cardId], getCardsById, {
    enabled: !!cardId,
    onSuccess: ({ data }) => {
      setCardById(data);
    },
  });
  useQuery(["getPanelById", panelId], getId, {
    enabled: !!panelId,
    onSuccess: ({ data }) => {
      setPanelById(data);
      previewById(data);
    },
    refetchOnWindowFocus: false,
  });
  const { mutate: previewPanelAPIData, isLoading: loadingData } =
    useMutation(previewPanel);

  const previewById = (data) => {
    //console.log("datavalue", data);
    const selectedRange = data.timePeriod;

    let formattedStart = "";
    let formattedEnd = "";
    const currentDateTime = dayjs();

    if (selectedRange) {
      if (selectedRange.includes("to")) {
        const [startString, endString] = selectedRange.split("to");
        formattedStart = startString;
        formattedEnd = endString;
      } else {
        if (
          selectedRange === "Last Hour" ||
          selectedRange === "Last 6 Hours" ||
          selectedRange === "Last 12 Hours" ||
          selectedRange === "Last 24 Hours"
        ) {
          const hours = {
            "Last Hour": 1,
            "Last 6 Hours": 6,
            "Last 12 Hours": 12,
            "Last 24 Hours": 24,
          };

          const lastXHours = currentDateTime.subtract(
            hours[selectedRange],
            "hour"
          );
          if (selectedRange === "Last Hour") {
            formattedStart = lastXHours.format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
          } else {
            formattedStart = lastXHours.format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
          }
        } else if (selectedRange === "Today") {
          formattedStart = currentDateTime
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss");
          formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
        } else if (selectedRange === "Yesterday") {
          const yesterday = currentDateTime.subtract(1, "day");
          formattedStart = yesterday
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss");
          formattedEnd = yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss");
        } else if (selectedRange === "Last Seven Days") {
          formattedStart = currentDateTime
            .subtract(6, "days")
            .startOf("day")
            .format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
        } else if (selectedRange === "Last Week") {
          formattedStart = currentDateTime
            .subtract(1, "week")
            .startOf("week")
            .format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime
            .subtract(1, "week")
            .endOf("week")
            .format("YYYY-MM-DD HH:00:00");
        } else if (selectedRange === "Last 30 Days") {
          formattedStart = currentDateTime
            .subtract(29, "days")
            .startOf("day")
            .format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime
            .endOf("day")
            .format("YYYY-MM-DD HH:00:00");
        } else if (selectedRange === "Last Month") {
          formattedStart = currentDateTime
            .subtract(1, "month")
            .startOf("month")
            .format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime
            .subtract(1, "month")
            .endOf("month")
            .format("YYYY-MM-DD HH:00:00");
        } else if (selectedRange === "This Month") {
          formattedStart = currentDateTime
            .startOf("month")
            .format("YYYY-MM-DD HH:mm:ss");
          formattedEnd = currentDateTime
            .endOf("day")
            .format("YYYY-MM-DD HH:mm:ss");
        }
      }
    }

    let reqData = {
      name: data.name,
      visualizationType: data.visualizationType,
      filters: [],
      dataColumns: {
        derivedFields: data.dataColumns ? data.dataColumns.derivedFields : [],
      },
      // startDate: convertToUTC(formattedStart),
      // endDate: convertToUTC(formattedEnd),
      startDate: formattedStart,
      endDate: formattedEnd,
    };
    if (data.visualizationType === "Bar Graph") {
      reqData.dataColumns["X-Axis"] = data.dataColumns["X-Axis"];
      reqData.dataColumns.noOfRecords = parseInt(data.dataColumns.noOfRecords);
    } else {
      reqData.dataColumns.tableFields = data?.dataColumns?.tableFields;
    }
    if (
      data.visualizationType === "Line Graph" ||
      data.visualizationType === "MultiAxis Graph"
    ) {
      reqData.interval = data.interval;
    }
    data.filters.forEach((condition) => {
      reqData.filters.push({
        field: condition.field,
        condition: condition.condition,
        value: condition.value,
        operator: condition.operator,
      });
    });
    // console.log("formattedStart", formattedStart);
    previewPanelAPIData(
      {
        reqData,
      },
      {
        onSuccess: ({ data }) => {
          setResponseData(data);
          setDate({ startDate: formattedStart, endDate: formattedEnd });
          //console.log("date123", date);
        },
        onError: (error) => {},
      }
    );
  };

  const getGridData = (index, dimension) => {
    let dim = { x: 0, y: 0, w: dimension.w, h: dimension.h };

    switch (dimension.order - 1) {
      case 0:
        return dim;
      case 1:
        dim.y = 3;
        return dim;
      case 2:
        dim.x = 6;
        return dim;
      case 3:
        dim.y = 3;
        return dim;
      case 4:
        dim.x = 6;
        dim.y = 3;
        return dim;
      default:
        return dim;
    }
  };
  const handleSearchInputChange = (value) => {
    setSearchQuery(value);
    setCardId("");
    setDashboardId("");
    setDashboardDetails([]);
    setResponseData([]);
    setCardDetails("");
    setCardDroppedData([]);
    setPreviewResponses([]);
    setPanelById("");
    setUserId("");
  };

  const handleSelectName = (id, name) => {
    setUserId(id);
    setSelectedName(name);
    setSearchQuery(name);
    setSearchClicked(false);
  };

  const handleClickSearch = () => {
    setSearchClicked(true);
  };

  const handleDashboard = (id) => {
    setCardDroppedData([]);
    setPreviewResponses([]);
    setDashboardId(id);
    setResponseData([]);
    setCardId("");
    setPanelId("");
    setPanelById("");
  };
  const handleCards = (id) => {
    setCardId(id);
    setDashboardId("");
    setResponseData("");
    setCardDroppedData([]);
    setPreviewResponses([]);
    setPanelId("");
    setPanelById("");
  };
  const handlePanel = (id) => {
    setPanelId(id);
    setCardId("");
    setDashboardId("");
    setCardDroppedData([]);
    setPreviewResponses([]);
  };
  let dimension = { w: 6, h: 4 };

  return (
    <div className="flex flex-row justify-between mt-10 w-full h-full">
      <div className="flex  mr-5">
        <div
          className=""
          // onClick={() => {
          //   navigate("/app/usermanagement");
          // }}
        >
          <BreadcrumbNavigation
            //linkOne={"Roles"}
            linkTwo={"Users"}
            onlinkTwoClick={() => navigate("/app/usermanagement")}
            title={"User Analysis"}
          />
          {/* <span className=" cursor-pointer">
            <Title title={" <  User Analysis "} />
          </span> */}

          {dashboardId ? (
            <div className="-ml-3 w-[68vw] h-[82%] relative mt-10">
              {cardDroppedData.length === 0 &&
              previewResponses.length === 0 &&
              !dashboardLoading ? (
                <div className="text-center md:mx-64 md:my-32 font-bold text-xl">
                  No cards or panels have been created for this dashboard
                </div>
              ) : (
                <>
                  <ResponsiveLayoutCard cardDroppedData={cardDroppedData} />
                  <ResponsiveLayout
                    cardDroppedData={cardDroppedData}
                    panelDroppedData={previewResponses}
                    getGridData={getGridData}
                    setPreviewLoading={setPreviewLoading}
                    previewLoading={previewLoading}
                  />
                </>
              )}
            </div>
          ) : null}

          {cardId ? (
            <div className="mt-3">
              <CardData card={cardById} />
            </div>
          ) : null}
          {loadingData && (
            <div className="mt-3 bg-white p-3 w-[65vw]">
              <div className="border-b border-panelBorder mt-2 mb-2"></div>
              <div className="my-3 flex justify-center items-center">
                <CircularProgress size={20} style={{ color: "black" }} />
              </div>
            </div>
          )}

          {!loadingData && responseData && panelById && (
            <div className="mt-10">
              <div className="bg-white p-3 w-[65vw] h-[70%]">
                <div className="ml-3 mt-2 text-xs">{panelById.name}</div>
                <div className="border-b mt-2 mx-2 border-panelBorder mb-2"></div>
                <div className="text-headingColor text-xs font-medium mb-2 mx-2">
                  {"From :"} {date?.startDate || ""}
                  {" - "}
                  {"To :"} {date?.endDate || ""}
                </div>

                <div className="text-headingColor text-xs font-medium flex items-center mb-2 mx-2">
                  <span>Selected Filters:</span>
                  <CssTooltip
                    title={<ConditionDisplay conditions={panelById?.filters} />}
                    placement="left"
                    arrow
                  >
                    <InfoIcon className="ml-2 w-4 h-3.5" />
                  </CssTooltip>
                </div>
                <PanelVisualization
                  type={panelById.visualizationType}
                  data={
                    panelById.visualizationType === "MultiAxis Graph"
                      ? responseData
                      : responseData?.data ?? []
                  }
                  colors={colors}
                  isChart={true}
                  dimension={dimension}
                  // columns={columns}
                />
              </div>
            </div>
          )}
        </div>
        {!dashboardId && !cardId && !panelById ? (
          <div className="mt-10 relative">
            <div className="absolute top-12 left-24 w-full h-full flex  justify-center text-black text-[10px] font-bold">
              <div>Search and analyze your users...</div>
            </div>
            <img
              src={bgImage}
              style={{
                height: "80%",
                width: "100%",
                objectFit: "cover",
              }}
              alt="bg"
            />
          </div>
        ) : null}
      </div>
      <div className="bg-white p-2.5 rounded md:w-[230px] w-full  ">
        <div className="border border-panelBorder p-2 rounded ">
          <input
            type="text"
            placeholder="Search user"
            className="border border-panelBorder rounded-sm px-1  w-full text-sm"
            style={{ outline: "none", borderColor: "transparent" }}
            value={searchQuery}
            onChange={(e) => handleSearchInputChange(e.target.value)}
            onClick={handleClickSearch}
          />
        </div>
        <div className=" flex-col rounded-sm px-1 py-1 mt-2">
          {searchClicked && (
            <>
              {filteredData === undefined ? (
                <div>Loading</div>
              ) : (
                <>
                  {filteredData.map((data) => (
                    <div
                      key={data.id}
                      className={` cursor-pointer hover:bg-selectBackground `}
                      onClick={() => handleSelectName(data.id, data.name)}
                    >
                      <span className="text-sm text-titleColor">
                        {data.name} -
                      </span>
                      <span className="text-xs text-tabColor">{data.role}</span>
                      <div className="border-b border-panelBorder mt-1 mb-1"></div>
                    </div>
                  ))}
                </>
              )}
            </>
          )}
        </div>

        {userId && dashboardDetails && cardDetails && panelDetails ? (
          <>
            <div>
              <AvailableItemsAccordion
                title="Dashboard"
                count={dashboardDetails?.length}
              >
                {dashboardDetails && dashboardDetails?.length > 0 ? (
                  dashboardDetails.map((details) => (
                    <div
                      className={`cursor-pointer hover:bg-selectBackground ${
                        dashboardId === details.id ? "bg-selectBackground" : ""
                      }`}
                      key={details.id}
                    >
                      <span
                        className="text-sm text-titleColor mx-5 whitespace-pre-wrap break-words mt-1"
                        onClick={() => {
                          handleDashboard(details.id);
                        }}
                      >
                        {details.name}
                      </span>
                      <div className="border-t border-panelBorder mt-2 mb-2"></div>
                    </div>
                  ))
                ) : (
                  <div className="my-1 d-flex justify-content-center font-medium text-xs">
                    No records to display
                  </div>
                )}
              </AvailableItemsAccordion>
            </div>
            <div>
              <AvailableItemsAccordion
                title="Cards"
                count={cardDetails?.length}
              >
                {cardDetails && cardDetails.length > 0 ? (
                  cardDetails.map((details) => (
                    <div
                      className={`cursor-pointer hover:bg-selectBackground ${
                        cardId === details.id ? "bg-selectBackground" : ""
                      }`}
                      key={details.id}
                    >
                      <div
                        className="text-sm text-titleColor mx-5 whitespace-pre-wrap break-words"
                        onClick={() => {
                          handleCards(details.id);
                        }}
                      >
                        {details.name}
                      </div>
                      <div className="border-t border-panelBorder mt-1 mb-1"></div>
                    </div>
                  ))
                ) : (
                  <div className="my-1 d-flex justify-content-center font-medium text-xs">
                    No records to display
                  </div>
                )}
              </AvailableItemsAccordion>
            </div>
            <div>
              <AvailableItemsAccordion
                title="Panels"
                count={panelDetails?.length}
              >
                {panelDetails && panelDetails?.length > 0 ? (
                  panelDetails.map((details) => (
                    <div
                      className={`cursor-pointer hover:bg-selectBackground ${
                        panelId === details.id ? "bg-selectBackground" : ""
                      }`}
                      key={details.id}
                    >
                      <div
                        className="text-sm text-titleColor mx-5 whitespace-pre-wrap break-words"
                        // style={{
                        //   whiteSpace: "pre-wrap",
                        //   wordWrap: "break-word",
                        //   //maxWidth: "180px",
                        // }}
                        onClick={() => {
                          handlePanel(details.id);
                        }}
                      >
                        {details.name}
                      </div>
                      <div className="border-t border-panelBorder mt-1 mb-1"></div>
                    </div>
                  ))
                ) : (
                  <div className="my-1 d-flex justify-content-center font-medium text-xs">
                    No records to display
                  </div>
                )}
              </AvailableItemsAccordion>
            </div>
          </>
        ) : null}
      </div>
    </div>
  );
}

export default UserAnalysis;
