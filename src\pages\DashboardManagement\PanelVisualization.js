import React, { useMemo } from "react";
import PreviewBarChart from "../../components/charts/PreviewBarChart";
import PreviewLineChart from "../../components/charts/PreviewLineChart";
import TablePreviewData from "./TablePreviewData";
import LineChartComponent from "../../components/charts/LineChart";
import BarChartComponent from "../../components/charts/BarCharts";
import Table from "../../components/table/ReportTable";
import DoughnutChart from "../../components/charts/DoughnutChart";
import dayjs from "dayjs";
import MultiAxisGraph from "../../components/charts/MultiAxisChart";
import PreviewMultiAxis from "./PreviewMultiAxis";

const PanelVisualization = ({
  type,
  data,
  colors,
  isChart,
  dimension,
  isWidth,
  maxWidthh,
}) => {
  const columns = useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    const firstItem = data[0];
    if (!firstItem) {
      return [];
    }

    const keys = Object.keys(firstItem);

    const dynamicColumns = keys.map((key) => ({
      header: key,
      accessorKey: key,
      Cell: ({ row }) => {
        const value = row.original[key];
        if (key === "Date") {
          return dayjs(value, { format: "YYYY-MM-DDTHH:mm:ss" }).isValid()
            ? dayjs(value).format("DD/MM/YYYY HH:mm:ss")
            : value;
        }
        return value;
      },
    }));

    return dynamicColumns;
  }, [data]);

  switch (type) {
    case "Bar Graph":
      return isChart ? (
        <BarChartComponent
          chartData={data}
          isChart={isChart}
          dimension={dimension}
          isWidth={isWidth}
        />
      ) : (
        <PreviewBarChart data={data} colors={colors} />
      );
    case "Line Graph":
      return isChart ? (
        <LineChartComponent
          data={data}
          colors={colors}
          isChart={isChart}
          dimension={dimension}
          isWidth={isWidth}
        />
      ) : (
        <PreviewLineChart data={data} colors={colors} />
      );
    case "Table Report":
      return isChart ? (
        <div className="h-fit">
          <Table
            data={data}
            columns={columns || []}
            dimension={dimension}
            isChart={true}
            maxWidthh={maxWidthh ? maxWidthh : "38vh"}
          />
        </div>
      ) : (
        <div className="mx-2">
          <TablePreviewData
            data={data}
            columns={columns || []}
            isPreview={true}
          />
        </div>
      );
    case "Pie Chart":
      return isChart ? (
        <div
          style={{
            display: "flex",
            //justifyContent: "space-between",
            width: "100%",
            height: "230px",
            margin: "auto",
          }}
        >
          <DoughnutChart
            className={"mx-auto"}
            respData={data || []}
            dimension={dimension}
            isChart={true}
            radius={110}
            width={700}
            height={500}
            cutout={60}
          />
        </div>
      ) : (
        <DoughnutChart
          //isChart={true}
          respData={data || []}
          dimension={dimension}
          preview={true}
        />
      );
    case "MultiAxis Graph":
      return isChart ? (
        <MultiAxisGraph
          data={data || []}
          dimension={dimension}
          ischart={true}
        />
      ) : (
        <div className="mx-2 overflow-x-auto">
          <PreviewMultiAxis data={data || []} colors={colors} />
        </div>
      );
    default:
      return null;
  }
};

export default PanelVisualization;
