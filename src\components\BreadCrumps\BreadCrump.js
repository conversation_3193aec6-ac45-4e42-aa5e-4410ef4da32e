import React from "react";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import Link from "@mui/material/Link";
import Typography from "@mui/material/Typography";
import { RightArrow } from "../../icons";

function BreadcrumbNavigation({ linkOne, linkTwo, title, onlinkTwoClick }) {
  return (
    <Breadcrumbs
      separator={<RightArrow className="h-4 w-4" />}
      aria-label="breadcrumb"
      className="sticky top-0 py-5"
    >
      {linkOne ? (
        <Link
          color="inherit"
          underline="hover"
          sx={{
            fontSize: "22px",
            fontFamily: "OpenSanHebrew",
            fontWeight: 700,
            "&:hover": { cursor: "pointer" },
          }}
        >
          {linkOne}
        </Link>
      ) : null}
      {linkTwo ? (
        <Link
          color="inherit"
          underline="hover"
          onClick={onlinkTwoClick}
          sx={{
            fontFamily: "OpenSanHebrew",
            fontSize: "22px",
            fontWeight: 700,
            "&:hover": { cursor: "pointer" },
          }}
        >
          {linkTwo}
        </Link>
      ) : null}
      {title ? (
        <Typography
          color="textPrimary"
          sx={{
            fontSize: "22px",
            fontFamily: "OpenSanHebrew",
            fontWeight: 700,
            "&:hover": { cursor: "auto" },
          }}
        >
          {title}
        </Typography>
      ) : null}
    </Breadcrumbs>
  );
}

export default BreadcrumbNavigation;
