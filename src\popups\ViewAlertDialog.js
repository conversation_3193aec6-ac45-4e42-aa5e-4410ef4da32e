import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

const ViewAlertDialog = ({ open, handleClose, data }) => {
  if (!data || data.length === 0) {
    return null;
  }

  const headers = data[0];
  const rows = data.slice(1);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "12px",
          overflow: "hidden",
          position: "absolute",
          top: "30%",
          left: "50%",
          transform: "translateX(-50%)",
          margin: 0,
        },
      }}
    >
      {/* Dialog Header */}
      <DialogTitle
        sx={{
          backgroundColor: "#2a57c9",
          color: "#fff",
          fontWeight: "bold",
          textAlign: "center",
          position: "relative",
        }}
      >
        Alert Details
        <IconButton
          onClick={handleClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: "#fff",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      {/* Dialog Content */}
      <DialogContent
        sx={{
          padding: 0,
          backgroundColor: "#f5f5f5",
        }}
      >
        <Table sx={{ borderCollapse: "separate", borderSpacing: 0 }}>
          {/* Table Header */}
          <TableHead>
            <TableRow>
              {headers.map((header, index) => (
                <TableCell
                  key={index}
                  sx={{
                    backgroundColor: "#d32f2f",
                    color: "#fff",
                    textAlign: "center",
                    fontWeight: "bold",
                    borderRight: "1px solid #fff", // Vertical line between columns
                    "&:last-child": {
                      borderRight: "none", // No border for the last column
                    },
                  }}
                >
                  {header}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>

          {/* Table Body */}
          <TableBody>
            {rows.map((row, rowIndex) => (
              <TableRow
                key={rowIndex}
                sx={{
                  "&:nth-of-type(even)": { backgroundColor: "#e3f2fd" },
                  "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                }}
              >
                {row.map((cell, cellIndex) => {
                  // Determine background color based on column index
                  let backgroundColor = "#ffffff"; // Default white background
                  if (headers[cellIndex].toLowerCase().includes("submission")) {
                    backgroundColor = "#e3f2fd"; // Light blue for submission columns
                  } else if (
                    headers[cellIndex].toLowerCase().includes("variation")
                  ) {
                    backgroundColor = "#e8f5e9"; // Light green for variation columns
                  }

                  return (
                    <TableCell
                      key={cellIndex}
                      sx={{
                        textAlign: "center",
                        borderRight: "1px solid #ccc", // Vertical line between columns
                        backgroundColor: backgroundColor, // Dynamic background color
                        "&:last-child": {
                          borderRight: "none", // No border for the last column
                        },
                      }}
                    >
                      {cell}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </DialogContent>
    </Dialog>
  );
};

export default ViewAlertDialog;
