import { useContext, useState } from "react";
import { FieldArray, Form, Formik } from "formik";
import TextFieldWrapper from "../components/FormsUI/TextField";
import Select from "../components/FormsUI/Select";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import BackButton from "../components/Button/Button";
import Button from "../components/Button/OutlinedButton";
import {
  timeIntervalOptions,
  timePeriodOptions,
  volumeTypeOptions,
} from "../common/constants";
import * as Yup from "yup";
import { DataContext } from "../context/DataContext";
import { logicalOperator } from "../common/constants";
import { useQuery } from "react-query";
import { getErrDescription, getResultCodes } from "../services/alert-api";

const AddAlertFormThree = ({ editDetail }) => {
  const {
    handleNextClick,
    setFormData,
    formData,
    handleNextClickStep,
    handlePrevClick,
    handlePrevStep,
  } = useContext(multiStepFormContext);
  const { setAlertDetails } = useContext(DataContext);
  const [errDescriptionOptions, setErrDescriptionOptions] = useState([]);
  const [resultCodeOptions, setResultCodeOptions] = useState([]);
  const [selectedErrCode, setSelectedErrCode] = useState("");

  useQuery(["/alerts"], getErrDescription, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      setErrDescriptionOptions(
        res?.data?.map((type) => ({
          value: type.value,
          label: type.label,
        })) || []
      );
    },
  });

  const { data: resultCodeData, isFetching: isFetchingResultCode } = useQuery(
    [
      "getResultCode",
      formData?.errorDescription ||
        selectedErrCode ||
        editDetail?.filters?.errorDescription,
    ],
    () =>
      getResultCodes(
        formData.errorDescription ||
          selectedErrCode ||
          editDetail?.filters?.errorDescription
      ),
    {
      enabled: !!(
        formData?.errorDescription ||
        selectedErrCode ||
        editDetail?.filters?.errorDescription
      ),
      refetchOnWindowFocus: false,
      onSuccess: (res) => {
        setResultCodeOptions(
          res?.data?.map((type) => ({
            value: type.value,
            label: type.label,
          })) || []
        );
      },
    }
  );

  const getInitialValues = () => {
    const commonFields = {
      threshold: formData?.threshold || editDetail?.filters?.threshold || "",
      timeInterval:
        formData?.timeInterval || editDetail?.evaluation_timeframe || "",
      timePeriod: formData?.timePeriod || editDetail?.compared_timerange || "",
    };

    switch (formData?.alertType) {
      case "Delivery Drop":
        return {
          ...commonFields,

          deviation: formData?.deviation || "",
          conditions: formData?.conditions?.length
            ? formData.conditions
            : [{ type1: "", type2: "", type3: "", type4: "" }],
          // conditions: [{ type1: "", type2: "", type3: "", type4: "" }],
        };

      case "Error":
        return {
          ...commonFields,
          errorDescription:
            formData?.errorDescription ||
            editDetail?.filters?.errorDescription ||
            "",
          resultCode:
            formData?.resultCode || editDetail?.filters?.resultCode || "",
        };

      case "Volume Drop or Spike":
        return {
          ...commonFields,
          minimumSubmissionCount:
            formData?.minimumSubmissionCount ||
            editDetail?.filters?.minSubmissionCount ||
            "",
          volumeType:
            formData?.volumeType ||
            editDetail?.metadata?.meta_additional_type ||
            "",
        };

      case "Delivery Report Pending":
        return {
          ...commonFields,
          minimumSubmissionCount:
            formData?.minSubmissionCount ||
            editDetail?.filters?.minSubmissionCount ||
            "",
        };

      default:
        return { ...commonFields };
    }
  };

  const initialValues = getInitialValues();

  const validationSchemas = {
    "Delivery Drop": Yup.object().shape({
      threshold: Yup.string().required("Delivery Threshold is required"),
      timeInterval: Yup.string().required("Time Interval is required"),
      deviation: Yup.string().required("Deviation is required"),
    }),
    Error: Yup.object().shape({
      errorDescription: Yup.string().required("Error Description is required"),
      resultCode: Yup.string().required("Result Code is required"),
      timePeriod: Yup.string().required("Time Period is required"),
      timeInterval: Yup.string().required("Time Interval is required"),
      threshold: Yup.string().required("Threshold is required"),
    }),
    "Volume Drop or Spike": Yup.object().shape({
      minimumSubmissionCount: Yup.string().required(
        "Minimum Submission Count is required"
      ),
      volumeType: Yup.string().required("Volume Type is required"),
      timePeriod: Yup.string().required("Time Period is required"),
      timeInterval: Yup.string().required("Time Interval is required"),
      threshold: Yup.string().required("Threshold is required"),
    }),
    "Delivery Report Pending": Yup.object().shape({
      minimumSubmissionCount: Yup.string().required(
        "Minimum Submission Count is required"
      ),
      timePeriod: Yup.string().required("Time Period is required"),
      timeInterval: Yup.string().required("Time Interval is required"),
      threshold: Yup.string().required("Threshold is required"),
    }),
  };
  const renderFields = ({ setFieldValue, values }) => {
    switch (formData?.alertType) {
      case "Delivery Drop":
        return (
          <>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Delivery Threshold %"} isMandatory={true} />
              <TextFieldWrapper name="threshold" placeholder="Enter value" />
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Time Period"} />
              <Select
                name="timePeriod"
                options={timePeriodOptions}
                placeholder="Select Time Period"
                setFormData={setFormData}
              />{" "}
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Time Interval"} isMandatory={true} />
              <Select
                name="timeInterval"
                options={timeIntervalOptions}
                placeholder="Select Time Interval"
                setFormData={setFormData}
              />{" "}
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Deviation %"} isMandatory={true} />
              <TextFieldWrapper
                name="deviation"
                placeholder="Enter deviation percentage"
              />
            </div>
          </>
        );
      case "Error":
        return (
          <>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Error Description"} isMandatory={true} />
              <Select
                name="errorDescription"
                options={errDescriptionOptions}
                placeholder="Select Error Description"
                onChange={(selectedOption) => {
                  const selectedType1Value = selectedOption.value;
                  setFieldValue("errorDescription", selectedType1Value);
                  setFieldValue("resultCode", "");
                  setSelectedErrCode(selectedType1Value);
                }}
              />
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Result Code"} isMandatory={true} />

              <Select
                name="resultCode"
                options={resultCodeOptions}
                placeholder={
                  isFetchingResultCode ? "Loading..." : "Select Result Code"
                }
                setFormData={setFormData}
                // setFormData={(value) => setFieldValue("resultCode", value)}
                // isDisabled={!values.errorDescription || isFetchingResultCode}
              />
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Time Period"} isMandatory={true} />
              <Select
                name="timePeriod"
                options={timePeriodOptions}
                placeholder="Select Time Period"
                setFormData={setFormData}
              />
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Time Interval"} isMandatory={true} />
              <Select
                name="timeInterval"
                options={timeIntervalOptions}
                placeholder="Select Time Interval"
                setFormData={setFormData}
              />{" "}
            </div>{" "}
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Threshold"} isMandatory={true} />
              <TextFieldWrapper
                name="threshold"
                placeholder="Enter Threshold"
              />{" "}
            </div>
          </>
        );
      case "Volume Drop or Spike":
        return (
          <>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel
                label={"Minimum Submission Count"}
                isMandatory={true}
              />
              <TextFieldWrapper
                name="minimumSubmissionCount"
                placeholder="Enter Minimum Submission Count"
              />
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Volume Type"} isMandatory={true} />
              <Select
                name="volumeType"
                options={volumeTypeOptions}
                placeholder="Select Volume Type"
                setFormData={setFormData}
              />{" "}
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Time Period"} isMandatory={true} />
              <Select
                name="timePeriod"
                options={timePeriodOptions}
                placeholder="Select Time Period"
                setFormData={setFormData}
              />
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Time Interval"} isMandatory={true} />
              <Select
                name="timeInterval"
                options={timeIntervalOptions}
                placeholder="Select Time Interval"
                setFormData={setFormData}
              />
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Threshold"} isMandatory={true} />
              <TextFieldWrapper
                name="threshold"
                placeholder="Enter Threshold"
              />
            </div>
          </>
        );
      case "Delivery Report Pending":
        return (
          <>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel
                label={"Minimum Submission Count"}
                isMandatory={true}
              />
              <TextFieldWrapper
                name="minimumSubmissionCount"
                placeholder="Enter Minimum Submission Count"
              />
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Time Period"} isMandatory={true} />
              <Select
                name="timePeriod"
                options={timePeriodOptions}
                placeholder="Select Time Period"
                setFormData={setFormData}
              />
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Time Interval"} isMandatory={true} />
              <Select
                name="timeInterval"
                options={timeIntervalOptions}
                placeholder="Select Time Interval"
                setFormData={setFormData}
              />
            </div>
            <div className="w-full md:w-[500px] mt-8">
              <InputLabel label={"Threshold"} isMandatory={true} />
              <TextFieldWrapper
                name="threshold"
                placeholder="Enter Threshold"
              />
            </div>
          </>
        );
      default:
        return null;
    }
  };

  const fieldList = [
    { label: "Total Submissions", value: "Total Submissions" },
    { label: "Submission Success", value: "Submission Success" },
    { label: "Submission Failure", value: "Submission Failure" },
    { label: "Delivery Success", value: "Delivery Success" },
    { label: "Delivery Failure", value: "Delivery Failure" },
  ];

  const conditionType = [
    { label: "Less than equal to", value: "Less than equal to" },
    { label: "Greater than equal to", value: "Greater than equal to" },
  ];

  return (
    <div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchemas[formData?.alertType]}
        onSubmit={(values) => {
          const keys = Object.keys(values);
          setAlertDetails((prevDetails) => {
            const newDetails = [...prevDetails];
            newDetails[2] = keys; // Store keys for step 3
            return newDetails;
          });
          handleNextClick();
          handleNextClickStep();
          let newData = Object.assign(formData, values);
          setFormData(newData);
        }}
      >
        {({ values, errors, status, touched, setFieldValue }) => (
          <Form>
            {formData?.alertType === "Delivery Drop" ? (
              <div className="md:flex gap-5 border border-outerBorder mx-12 pt-4 pl-4">
                <div className="flex flex-col items-start gap-3">
                  <FieldArray
                    name="conditions"
                    render={({ insert, remove, push }) => {
                      return (
                        <>
                          <div className="flex flex-col md:flex-row items-center justify-center gap-3">
                            <div>
                              {values?.conditions?.map((condition, index) => {
                                const selectedFilters = values.conditions.map(
                                  (cond) => cond.type1
                                );

                                const submissionFilters = [
                                  "Total Submissions",
                                  "Submission Success",
                                  "Submission Failure",
                                ];

                                const deliveryFilters = [
                                  "Delivery Success",
                                  "Delivery Failure",
                                ];

                                // Check if any Submission filter is selected
                                const hasSubmissionSelected =
                                  selectedFilters.some((f) =>
                                    submissionFilters.includes(f)
                                  );

                                // Check if any Delivery filter is selected
                                const hasDeliverySelected =
                                  selectedFilters.some((f) =>
                                    deliveryFilters.includes(f)
                                  );

                                // Disable options dynamically based on selected filters
                                const updatedOptions = fieldList.map(
                                  (option) => ({
                                    ...option,
                                    isDisabled:
                                      (submissionFilters.includes(
                                        option.value
                                      ) &&
                                        hasSubmissionSelected &&
                                        !submissionFilters.includes(
                                          condition.type1
                                        )) ||
                                      (deliveryFilters.includes(option.value) &&
                                        hasDeliverySelected &&
                                        !deliveryFilters.includes(
                                          condition.type1
                                        )),
                                  })
                                );
                                return (
                                  <div
                                    key={index}
                                    className="flex flex-col md:flex-row gap-5 items-center justify-center"
                                  >
                                    <div className="flex flex-col mb-4">
                                      <InputLabel label={"Field"} />
                                      <Select
                                        className="w-full md:w-[200px] lg:w-[210px]"
                                        name={`conditions.${index}.type1`}
                                        options={updatedOptions} // Pass dynamically disabled options
                                        placeholder={"Select Field"}
                                        onChange={(selectedOption) => {
                                          setFieldValue(
                                            `conditions.${index}.type1`,
                                            selectedOption.value
                                          );
                                        }}
                                        isSearchable={true}
                                      />
                                    </div>
                                    <div className="flex flex-col mb-4">
                                      <InputLabel label={"Condition"} />
                                      <Select
                                        className="w-full md:w-[200px] lg:w-[210px]"
                                        name={`conditions.${index}.type2`}
                                        options={conditionType}
                                        placeholder={"Select Condition"}
                                      />
                                    </div>

                                    <div className="flex flex-col mb-4">
                                      <InputLabel label={"Value"} />
                                      <TextFieldWrapper
                                        className="w-full md:w-[200px] lg:w-[210px]"
                                        name={`conditions.${index}.type3`}
                                        placeholder={"Enter Value"}
                                        disabled={
                                          !values.conditions[index].type1 ||
                                          values.conditions[index].type1 ===
                                            "" ||
                                          !values.conditions[index].type2 ||
                                          values.conditions[index].type2 === ""
                                        }
                                      />
                                    </div>
                                    <div className="flex flex-col mb-4">
                                      <InputLabel label={"Operator"} />
                                      <Select
                                        className="w-full md:w-[130px] lg:w-[180px]"
                                        name={`conditions.${index}.type4`}
                                        options={logicalOperator}
                                        placeholder={"Select Logic"}
                                        isDisabled={
                                          !values.conditions[index].type1 ||
                                          values.conditions[index].type1 === ""
                                        }
                                      />
                                    </div>

                                    <div className="">
                                      <button
                                        type="button"
                                        className="rounded-2xl w-8 h-8 bg-gray-300 p-2 flex items-center justify-center"
                                        onClick={() => {
                                          if (values.conditions.length === 1) {
                                            setFieldValue(
                                              `conditions.${index}.type1`,
                                              ""
                                            );
                                            setFieldValue(
                                              `conditions.${index}.type2`,
                                              ""
                                            );
                                            setFieldValue(
                                              `conditions.${index}.type3`,
                                              ""
                                            );
                                            setFieldValue(
                                              `conditions.${index}.type4`,
                                              ""
                                            );
                                          } else {
                                            remove(index);
                                          }
                                        }}
                                      >
                                        <span className="text-xl font-bold">
                                          -
                                        </span>
                                      </button>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                          <div className="flex-grow flex justify-end">
                            <button
                              type="button"
                              className="w-8 h-8 p-2 "
                              onClick={() => {
                                const allFieldsFilled =
                                  values.conditions?.every(
                                    (condition) =>
                                      condition.type1 &&
                                      condition.type2 &&
                                      condition.type3 &&
                                      condition.type4
                                  );
                                if (allFieldsFilled) {
                                  push({
                                    type1: "",
                                    type2: "",
                                    type3: "",
                                    type4: "",
                                  });
                                }
                              }}
                              disabled={
                                !values.conditions?.every(
                                  (condition) =>
                                    condition.type1 &&
                                    condition.type2 &&
                                    condition.type3 &&
                                    condition.type4
                                ) ||
                                values.conditions.some(
                                  (condition) => condition.type4 === "no"
                                )
                              }
                            >
                              <span className="text-sm font-bold">+Add</span>
                            </button>
                          </div>
                        </>
                      );
                    }}
                  />
                </div>
              </div>
            ) : null}
            <div className="flex flex-col items-center justify-center mb-4 mx-10 md:mx-0">
              {renderFields({ setFieldValue, values })}
            </div>
            <div className="flex-grow flex justify-end items-center mx-20 mt-20 gap-4">
              <BackButton
                label={"Back"}
                buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded"
                onClick={handlePrevClick}
              />
              <Button
                block="true"
                type="submit"
                label="Next"
                value="submit"
                buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded "
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default AddAlertFormThree;
