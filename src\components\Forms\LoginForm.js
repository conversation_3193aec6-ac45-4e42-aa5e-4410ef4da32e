import React, { useContext, useState } from "react";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";
import { AuthContext } from "../../context/AuthContext";
import { useTranslation } from "react-i18next";
import { Link, useNavigate } from "react-router-dom";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { DataContext } from "../../context/DataContext";
import { emailValidation } from "../../common/yupValidation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import LoginButton from "../../components/Button/OutlinedButton";
import InputLabel from "../FormsUI/InputLabel/InputLabel";
import { useQuery } from "react-query";
import { ConfigService } from "../../services/config-api";
import { encryptPassword } from "../../common/commonFunctions";

function LoginForm() {
  const style = {
    asterisk: {
      color: "red",
      marginLeft: "3px",
    },
    fieldstyle:
      "form-control  w-full mt-2 text-sm pl-5 text-black bg-white bg-clip-padding border border-solid border-Bittersweet rounded transition ease-in-out  m-0 inline-block h-12  font-semibold ",
  };

  const { login } = useContext(AuthContext);
  const [showPassword, setShowPassword] = useState(false);
  const { setEmail, setPassword } = useContext(DataContext);
  const navigate = useNavigate();
  const [publicKey, setPublicKey] = useState(null);
  const { t } = useTranslation();

  useQuery(["key"], ConfigService.getPublicKey, {
    refetchOnWindowFocus: false,
    onSuccess: ({ data }) => {
      setPublicKey(data.publicKey);
    },
  });

  return (
    <div className="font-sans">
      <Formik
        initialValues={{ email: "", password: "" }}
        validationSchema={Yup.object().shape({
          email: emailValidation,
          password: Yup.string().required("Password is required"),
        })}
        onSubmit={async ({ email, password }, { setSubmitting }) => {
          const encryptedPassword = await encryptPassword(password, publicKey);
          // console.log("encryptedPassword", encryptedPassword);
          if (!encryptedPassword) {
            console.log("Encryption failed");

            setSubmitting(false);
            return;
          }

          setEmail(email);
          setPassword(encryptedPassword);
          setSubmitting(true);

          login(email, encryptedPassword, (resp) => {
            if (resp?.data?.otpSent === true) {
              navigate("/auth/verify-otp");
            }
          })
            .catch((error) => {
              toast.error(error?.response?.data?.message || error.message);
            })
            .finally(() => {
              setSubmitting(false);
            });
        }}
      >
        {({ errors, status, touched, isSubmitting }) => (
          <Form>
            <div className="w-full">
              <InputLabel label={"_Email_Id"} isMandatory={true} />
              <div className="relative">
                <Field
                  className={style.fieldstyle}
                  as="input"
                  name="email"
                  type="email"
                  placeholder="Enter the Email Id"
                />
                {errors.email && touched.email && (
                  <p className="text-errorColor text-xs mt-1">{errors.email}</p>
                )}
              </div>
            </div>

            <div className="mt-6 w-full">
              <InputLabel label={"Password"} isMandatory={true} />
              <div className="relative h-16">
                <Field name="password">
                  {({ field, form }) => (
                    <input
                      {...field}
                      className={style.fieldstyle}
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter the Password"
                      onKeyDown={(e) => {
                        if (e.key === "Backspace" || e.key === "Delete") {
                          e.preventDefault();
                          form.setFieldValue("password", "");
                        }
                      }}
                    />
                  )}
                </Field>

                <div
                  className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                  onClick={() => setShowPassword((prev) => !prev)}
                >
                  {showPassword ? <FaEye /> : <FaEyeSlash />}
                </div>

                {errors.password && touched.password && (
                  <p className="text-errorColor text-xs mt-1">
                    {errors.password}
                  </p>
                )}
              </div>
            </div>

            <p className="text-right">
              <span className="text-[#4977EB] underline underline-offset-1 cursor-pointer text-xs font-semibold">
                <Link to="/auth/forgot-password">{t("_Forgot_password?")}</Link>
              </span>
            </p>

            <LoginButton
              type="submit"
              label={"Log in"}
              buttonClassName="w-full text-sm mb-3 rounded mt-3"
              loading={isSubmitting}
            />
            {status && (
              <p className="text-errorColor text-xs " valid={false}>
                {status}
              </p>
            )}
          </Form>
        )}
      </Formik>

      <ToastContainer position="top-center" autoClose={3000} />
    </div>
  );
}

export default LoginForm;
