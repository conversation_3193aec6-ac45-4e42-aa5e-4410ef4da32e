import { config } from "../assets/config/config";
const apiUrl = config.api.url;
//const apiUrl = "https://smshub.openturf.dev/smshub_dev";
export const urlBase = process.env.REACT_APP_API_URL;

var APIMapping = {
  login: "/v1/login",
  signup: "v2/customers/_register",
  forgotpassword: "v2/customers/forgot-password",
  resetpassword: "api/v2/customers/reset-password",
  verifymobileno: "api/v2/customers/verify-mobile",
  publicKey: "/v1/public-key",
  card: "/v1/card",
  configuration: "/v1/users/config",
  user: "/v1/users",
  logs: "/v1/users/logs",
  reports: "/v1/reports/static",
  panels: "/v1/panel",
  roles: "/v1/roles",
  panelPreview: "/v1/panel/preview",
  alertGroups: "/v1/alert/group",
  dashboard: "/v1/dashboard",
  viewDashboard: "/v1/dashboard/view/{id}",
  visualizationType: "/v1/panel/visualization",
  panelProperty: "/v1/panel/properties",
  supplierData: "/v1/reports/suppliers",
  customerData: "/v1/reports/customers",
  lcrData: "/v1/reports/lcr",
  cdrStatusData: "/v1/reports/cdr-status",
  reportsFilter: "/v1/reports/search-fields",
  sourcePrimeFilter: "/v1/reports/source-prime",
  destinationPrimeFilter: "/v1/reports/destination-prime",
  derivedFieldData: "/v1/reports/derived-fields",
  destinationReports: "/v1/reports/destination",
  eventList: "/v1/users/logs/events",
  rolesList: "/v1/roles/list",
  classifiedReports: "/v1/reports/classify",
  alerts: "/v1/alert",
  getAlertsHistory: "/v1/alert/history/",
  alertMetadata: "/v1/alert/metadata",
  alertNotify: "/v1/alert/notifications",
  billingRefresh: "/v1/reports/billing-reports",
};
function getAPIMap(name) {
  ////console.log("finalurl", process.env.IP_ADDR + APIMapping[name]);
  return apiUrl + APIMapping[name];
}

export default getAPIMap;
