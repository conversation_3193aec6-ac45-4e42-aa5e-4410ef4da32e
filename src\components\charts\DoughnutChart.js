import React from "react";
import { ResponsivePie } from "@nivo/pie";

const colors = [
  "#A1C3FA",
  "#EDDF82",
  "#FFAB2D",
  "#FF5733",
  "#6C5CE7",
  "#48DBFB",
];

const formatDataForPieChart = (respData) => {
  const totalValue = Object.values(respData).reduce(
    (sum, value) => sum + value,
    0
  );
  return Object.entries(respData).map(([label, value], index) => ({
    id: label,
    label: label,
    value,
    color: colors[index % colors.length],
    percentage:
      totalValue === 0 ? "0.00" : ((value / totalValue) * 100).toFixed(2),
  }));
};

const DoughnutChart = ({ respData, panelData, reportData }) => {
  const formattedData = formatDataForPieChart(respData);
  const totalValue = Object.values(respData).reduce(
    (sum, value) => sum + value,
    0
  );
  const arcLabel = (d) => {
    return `${d.value}(${d.data.percentage}%)`;
  };

  return (
    <ResponsivePie
      data={formattedData}
      margin={{ top: 10, right: 80, bottom: 100, left: 80 }}
      innerRadius={0.5}
      padAngle={1}
      activeOuterRadiusOffset={10}
      borderWidth={2}
      borderColor={{
        from: "color",
        modifiers: [["darker", 0.2]],
      }}
      colors={{ datum: "data.color" }}
      arcLinkLabelsSkipAngle={360}
      arcLabelsSkipAngle={10}
      arcLabelsTextColor={{
        from: "color",
        modifiers: [["darker", 6]],
      }}
      arcLabel={arcLabel}
      theme={{
        labels: {
          text: {
            fontSize: panelData ? 14 : 8,
            fontWeight: "bold",
            color: "black",
          },
        },
      }}
      animate={false}
      tooltip={({ datum }) => (
        <div
          style={{
            background: "black",
            color: "white",
            padding: "5px 10px",
            fontSize: "12px",
            borderRadius: "3px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <div
            style={{
              width: "12px",
              height: "12px",
              backgroundColor: datum.color,
              marginRight: "8px",
              borderRadius: "2px",
            }}
          />
          <div>
            <strong>{datum.id}</strong>: {datum.value} ({datum.data.percentage}
            %)
          </div>
        </div>
      )}
      layers={[
        "arcs",
        "arcLabels",
        "legends",
        ({ centerX, centerY }) => (
          <text
            x={centerX}
            y={centerY}
            textAnchor="middle"
            dominantBaseline="central"
            style={{
              fontSize: panelData ? "16px" : "10px",
              fontWeight: "bold",
            }}
          >
            {`Total: ${totalValue}`}
          </text>
        ),
      ]}
      height={panelData ? 450 : reportData ? 390 : 310}
    />
  );
};

export default DoughnutChart;
