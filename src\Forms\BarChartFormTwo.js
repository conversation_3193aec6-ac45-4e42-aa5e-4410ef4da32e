import React, { useContext, useState, useEffect } from "react";
import { Form, Formik, FieldArray, useFormikContext } from "formik";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import Select from "../components/FormsUI/Select";
import BackButton from "../components/Button/Button";
import Button from "../components/Button/OutlinedButton";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import { getPanelProperties } from "../services/dropdown-api";
import { useQuery } from "react-query";
import TextFieldWrapper from "../components/FormsUI/TextField";
import CustomDropDown from "../components/Dropdown/CustomeDropdown";
import * as Yup from "yup";
import {
  supplierList,
  customerList,
  destinationCountry,
  destinationName,
} from "../services/dropdown-api";
import { logicalOperator, recordsPanel } from "../common/constants";
import SelectedFilters from "../common/selectedFilters";
import InfoModal from "../components/modals/InfoModal";
import {
  customerInterface,
  customerProtocol,
  supplierProtocol,
} from "../common/constants";
import { DataContext } from "../context/DataContext";

function BarChartFormTwo({ editDetail, isEditBarLoading, configApiData }) {
  const [valueData, setValueData] = useState([]);
  const [customerListData, setCustomerListData] = useState([]);
  const [derivedFieldList, setDerivedFieldList] = useState([]);
  const [fieldList, setFieldList] = useState([]);
  const [response, setResponse] = useState("");
  const [conditionList, setConditionList] = useState([]);
  const [optionDataList, setOptionDataList] = useState([]);
  const [destnationNameList, setDestinationNameList] = useState([]);
  const [destinationCountryList, setDestinationCountryList] = useState([]);
  const [field, setField] = useState([]);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [message, setMessage] = useState("");
  const [msg, setMsg] = useState("");
  const {
    handleNextClick,
    setFormData,
    formData,
    handlePrevClick,
    handleNextClickStep,
    handlePrevStep,
  } = useContext(multiStepFormContext);
  const { previousValue } = useContext(DataContext);

  // Queries
  useQuery(["/supplierData"], supplierList, {
    refetchOnWindowFocus: false,

    onSuccess: (res) => {
      const supplierListData =
        res?.data
          ?.map((type) => ({ value: type.name, label: type.name }))
          .filter(
            (item, index, self) =>
              index === self.findIndex((t) => t.label === item.label)
          ) || [];
      setValueData(supplierListData);
    },
  });

  useQuery(["/customerData"], customerList, {
    refetchOnWindowFocus: false,

    onSuccess: (res) => {
      const customerListData =
        res?.data
          ?.map((type) => ({ value: type.name, label: type.name }))
          .filter(
            (item, index, self) =>
              index === self.findIndex((t) => t.label === item.label)
          ) || [];
      setCustomerListData(customerListData);
    },
  });

  useQuery(["/destinationCountryData"], destinationCountry, {
    refetchOnWindowFocus: false,

    onSuccess: (res) => {
      setDestinationCountryList(
        res?.data?.map((type) => ({ value: type, label: type })) || []
      );
    },
  });

  useQuery(["/destinationNameData"], destinationName, {
    refetchOnWindowFocus: false,

    onSuccess: (res) => {
      setDestinationNameList(
        res?.data?.map((type) => ({ value: type, label: type })) || []
      );
    },
  });

  const { data: panelProperty, isLoading } = useQuery(
    ["/panelproperty", formData?.type],
    getPanelProperties,
    {
      refetchOnWindowFocus: false,

      onSuccess: (res) => {
        if (res?.data) {
          const fieldData =
            res.data.filters?.map((type) => ({
              value: type.field,
              label: type.field,
            })) || [];

          const derivedFields =
            res.data.columns?.derivedFields?.map((type) => ({
              value: type,
              label: type,
            })) || [];

          const xAxisFields =
            res.data.columns?.["X-Axis"]?.map((type) => ({
              value: type,
              label: type,
            })) || [];

          setFieldList(fieldData);
          setDerivedFieldList(derivedFields);
          setField(xAxisFields);
        }
      },
      onError: (error) => {
        console.error("Error fetching panel property:", error);
      },
    }
  );

  const validationSchema = Yup.object().shape({
    field: Yup.array()
      .min(1, "Field is required")
      .required("Field is required"),

    derivedField: Yup.array()
      .min(1, "Derived Field is required")
      .required("Derived Field is required"),
  });
  useEffect(() => {
    if (panelProperty && panelProperty?.data && response !== "") {
      const selectedCondition =
        panelProperty?.data?.filters?.find((field) => field?.field === response)
          ?.condition || [];
      const conditionType = selectedCondition.map((type) => ({
        value: type,
        label: type,
      }));
      setConditionList(conditionType);
    }
  }, [panelProperty, response]);

  useEffect(() => {
    let conditions = [];
    if (formData && formData.conditions) {
      conditions = formData.conditions;
    } else if (editDetail && editDetail.filters) {
      conditions = editDetail.filters.map((condition) => ({
        type1: condition.field,
      }));
    }

    const newOptionDataList = conditions.map((condition, index) => {
      const fieldValue = condition.type1;
      setResponse(fieldValue);
      let optionDataForCondition = [];
      if (fieldValue === "Customer Name" && customerListData.length > 0) {
        optionDataForCondition = customerListData;
      } else if (fieldValue === "Supplier Name" && valueData.length > 0) {
        optionDataForCondition = valueData;
      } else if (
        fieldValue === "Destination Operator Country" &&
        destinationCountryList.length > 0
      ) {
        optionDataForCondition = destinationCountryList;
      } else if (
        fieldValue === "Destination Operator Name" &&
        destnationNameList.length > 0
      ) {
        optionDataForCondition = destnationNameList;
      } else if (
        fieldValue === "Customer Interface" &&
        customerInterface.length > 0
      ) {
        optionDataForCondition = customerInterface;
      } else if (
        fieldValue === "Customer Protocol" &&
        customerProtocol.length > 0
      ) {
        optionDataForCondition = customerProtocol;
      } else if (
        fieldValue === "Supplier Protocol" &&
        supplierProtocol.length > 0
      ) {
        optionDataForCondition = supplierProtocol;
      } else if (
        fieldValue === "Source Operator Name" &&
        destnationNameList.length > 0
      ) {
        optionDataForCondition = destnationNameList;
      }
      return optionDataForCondition;
    });
    setOptionDataList(newOptionDataList);
  }, [
    formData,
    editDetail,
    customerListData,
    valueData,
    destnationNameList,
    destinationCountryList,
  ]);

  const defaultXAxis =
    formData.field || editDetail?.dataColumns?.["X-Axis"] || [];

  const defaultDerivedField = (
    formData.derivedField ||
    editDetail?.dataColumns?.derivedFields ||
    []
  ).filter((field) =>
    panelProperty?.data?.columns?.derivedFields?.includes(field)
  );
  const handleSubmit = (values) => {
    const multipleCondition = configApiData?.FIELDS_WITH_MULTIPLE_CONDITIONS;

    const condition = values.conditions[0];
    const isMatched =
      values.field.find((condition) => {
        if (multipleCondition.includes(condition)) {
          return true;
        }
        return false;
      }) || "";

    const multipleConditionType1 =
      values.conditions.find((condition) =>
        multipleCondition.includes(condition.type1)
      )?.type1 || "";

    const hasMultipleConditionField = multipleConditionType1 !== "";
    const numConditions = values.conditions.length;

    if (hasMultipleConditionField && numConditions < 2) {
      setShowAlertConfirmation(true);
      setMsg(`Oops! ${multipleConditionType1} alone cannot display report.`);
      setMessage(
        `We need one more filter with ${multipleConditionType1} to generate the report.`
      );
      return;
    }

    const lastCondition = values.conditions[numConditions - 1];
    const isLastConditionEmpty =
      !lastCondition.type1 || !lastCondition.type2 || !lastCondition.type3;

    if (
      hasMultipleConditionField &&
      isLastConditionEmpty &&
      numConditions === 2
    ) {
      setShowAlertConfirmation(true);
      setMsg("");
      setMessage("All conditions must be filled out.");
      return;
    }

    if (
      isMatched &&
      (condition.type1 === "" ||
        condition.type2 === "" ||
        condition.type3 === "")
    ) {
      if (
        condition.type1 === "" &&
        condition.type2 === "" &&
        condition.type3 === ""
      ) {
        setShowAlertConfirmation(true);
        setMessage(
          "Please at least select one filter condition for the selected x axis field"
        );
        setMsg("");
      } else {
        setShowAlertConfirmation(true);
        setMsg("");
        setMessage("All conditions are mandatory.");
      }
      return;
    }
    const anyConditionEmptyType = values.conditions.find(
      (condition) =>
        condition.type1 !== "" &&
        (condition.type2 === "" || condition.type3 === "")
    );

    if (anyConditionEmptyType) {
      setShowAlertConfirmation(true);
      setMsg("");
      setMessage("All conditions must be filled out.");
      return;
    }

    handleNextClick();
    handleNextClickStep();
    setFormData({ ...formData, ...values });
  };

  //console.log("formData.type", formData.type);
  // console.log("editDetail.visualizationType", editDetail.visualizationType);
  const initialValues = {
    conditions: [{ type1: "", type2: "", type3: "", type4: "" }],
    field: defaultXAxis,
    derivedField: defaultDerivedField,
    records:
      formData.conditions?.length === 1 &&
      formData.conditions[0].type1 === "" &&
      formData.conditions[0].type2 === "" &&
      formData.conditions[0].type3 === "" &&
      formData.conditions[0].type4 === "" &&
      formData.derivedField?.length === 0 &&
      formData.field?.length === 0
        ? "5"
        : (
            formData?.records || editDetail?.dataColumns?.noOfRecords
          )?.toString() ?? "5",
  };
  if (
    formData.type !== "" &&
    editDetail.visualizationType !== undefined &&
    formData.type !== editDetail.visualizationType &&
    !previousValue
  ) {
    initialValues.conditions = [{ type1: "", type2: "", type3: "", type4: "" }];
  } else if (
    !formData.conditions &&
    editDetail &&
    editDetail.filters &&
    editDetail.filters.length > 0
  ) {
    initialValues.conditions = editDetail.filters.map((filter) => ({
      type1: filter.field,
      type2: filter.condition,
      type3: filter.value,
      type4: filter.operator,
    }));
  } else if (formData && formData.conditions && previousValue) {
    initialValues.conditions = formData.conditions;
  }
  return (
    <>
      <div>
        {isLoading ? (
          <div className="flex justify-center items-center">Loading...</div>
        ) : (
          <Formik
            initialValues={initialValues}
            enableReinitialize={true}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({
              values,
              errors,
              status,
              touched,
              setFieldValue,
              //initialValues,
            }) => (
              <Form>
                <SelectedFilters
                  values={values}
                  fieldList={fieldList}
                  conditionList={values.conditions.map(
                    (condition) => condition.type2
                  )}
                  logicalOperator={logicalOperator}
                />
                {/* {console.log("initialValues", initialValues)} */}
                <div className="md:flex gap-5 border border-outerBorder mx-12 pt-4 pl-4">
                  <div className="flex flex-col items-start gap-3">
                    <FieldArray
                      name="conditions"
                      render={({ insert, remove, push }) => {
                        return (
                          <>
                            <div className="flex flex-col md:flex-row items-center justify-center gap-3">
                              <div>
                                {values.conditions.map((condition, index) => {
                                  let conditionMatched = false;

                                  return (
                                    <div
                                      key={index}
                                      className="flex flex-col md:flex-row gap-5 items-center justify-center"
                                    >
                                      <div className="flex flex-col  mb-4">
                                        <InputLabel label={"Field"} />
                                        <Select
                                          className="w-full  md:w-[200px] lg:w-[210px]"
                                          name={`conditions.${index}.type1`}
                                          options={fieldList}
                                          placeholder={"Select Field"}
                                          onChange={(selectedOption) => {
                                            const selectedType1Value =
                                              selectedOption.value;
                                            setFieldValue(
                                              `conditions.${index}.type1`,
                                              selectedType1Value
                                            );
                                            setResponse(selectedType1Value);

                                            let updatedOptionData = [];
                                            if (
                                              selectedType1Value ===
                                                "Customer Name" &&
                                              customerListData.length > 0
                                            ) {
                                              updatedOptionData =
                                                customerListData;
                                            } else if (
                                              selectedType1Value ===
                                                "Supplier Name" &&
                                              valueData.length > 0
                                            ) {
                                              updatedOptionData = valueData;
                                            } else if (
                                              selectedType1Value ===
                                                "Destination Operator Country" &&
                                              destinationCountryList.length > 0
                                            ) {
                                              updatedOptionData =
                                                destinationCountryList;
                                            } else if (
                                              selectedType1Value ===
                                                "Destination Operator Name" &&
                                              destnationNameList.length > 0
                                            ) {
                                              updatedOptionData =
                                                destnationNameList;
                                            } else if (
                                              selectedType1Value ===
                                                "Customer Interface" &&
                                              customerInterface.length > 0
                                            ) {
                                              updatedOptionData =
                                                customerInterface;
                                            } else if (
                                              selectedType1Value ===
                                                "Customer Protocol" &&
                                              customerProtocol.length > 0
                                            ) {
                                              updatedOptionData =
                                                customerProtocol;
                                            } else if (
                                              selectedType1Value ===
                                                "Supplier Protocol" &&
                                              supplierProtocol.length > 0
                                            ) {
                                              updatedOptionData =
                                                supplierProtocol;
                                            } else if (
                                              selectedType1Value ===
                                                "Source Operator Name" &&
                                              destnationNameList.length > 0
                                            ) {
                                              updatedOptionData =
                                                destnationNameList;
                                            }
                                            const newOptionDataList = [
                                              ...optionDataList,
                                            ];
                                            newOptionDataList[index] =
                                              updatedOptionData;
                                            setOptionDataList(
                                              newOptionDataList
                                            );
                                          }}
                                          isSearchable={true}
                                        />
                                      </div>
                                      <div className="flex flex-col mb-4">
                                        <InputLabel label={"Condition"} />
                                        {!values.conditions[index].type1 ? (
                                          <Select
                                            className="w-full  md:w-[200px] lg:w-[210px]"
                                            name={`conditions.${index}.type2`}
                                            options={[]}
                                            placeholder={"Select Condition"}
                                            isDisabled={
                                              !values.conditions[index].type1 ||
                                              values.conditions[index].type1 ===
                                                ""
                                            }
                                          />
                                        ) : (
                                          panelProperty?.data?.filters &&
                                          panelProperty.data.filters
                                            .filter(
                                              (field) =>
                                                field.field ===
                                                values.conditions[index].type1
                                            )
                                            .map((filter, filterIndex) => {
                                              const selectedCondition =
                                                filter.condition || [];
                                              const conditionType =
                                                selectedCondition.map(
                                                  (type) => ({
                                                    value: type,
                                                    label: type,
                                                  })
                                                );
                                              return (
                                                <Select
                                                  key={filterIndex}
                                                  className="w-full  md:w-[200px] lg:w-[210px]"
                                                  name={`conditions.${index}.type2`}
                                                  options={conditionType}
                                                  placeholder={
                                                    "Select Condition"
                                                  }
                                                  isDisabled={
                                                    !values.conditions[index]
                                                      .type1 ||
                                                    values.conditions[index]
                                                      .type1 === ""
                                                  }
                                                />
                                              );
                                            })
                                        )}
                                      </div>

                                      <div className="flex flex-col mb-4">
                                        <InputLabel label={"Value"} />

                                        {panelProperty?.data?.filters.map(
                                          (filter, filterIndex) => {
                                            const isDropdown =
                                              filter.datatype === "dropdown";
                                            const isMatched =
                                              values.conditions[index].type1 ===
                                              filter.field;

                                            if (isMatched && isDropdown) {
                                              conditionMatched = true;
                                              return (
                                                <Select
                                                  key={filter.field}
                                                  className="w-full  md:w-[200px] lg:w-[210px]"
                                                  name={`conditions.${index}.type3`}
                                                  options={
                                                    optionDataList[index]
                                                  }
                                                  placeholder={"Select Value"}
                                                  isDisabled={
                                                    !values.conditions[index]
                                                      .type1 ||
                                                    values.conditions[index]
                                                      .type1 === "" ||
                                                    !values.conditions[index]
                                                      .type2 ||
                                                    values.conditions[index]
                                                      .type2 === ""
                                                  }
                                                  isSearchable={true}
                                                />
                                              );
                                            } else if (
                                              isMatched &&
                                              !isDropdown
                                            ) {
                                              conditionMatched = true;
                                              return (
                                                <TextFieldWrapper
                                                  key={filter.field}
                                                  className="w-full  md:w-[200px] lg:w-[210px]"
                                                  name={`conditions.${index}.type3`}
                                                  placeholder={"Enter Value"}
                                                  disabled={
                                                    !values.conditions[index]
                                                      .type1 ||
                                                    values.conditions[index]
                                                      .type1 === "" ||
                                                    !values.conditions[index]
                                                      .type2 ||
                                                    values.conditions[index]
                                                      .type2 === ""
                                                  }
                                                />
                                              );
                                            }
                                          }
                                        )}

                                        {!conditionMatched && (
                                          <TextFieldWrapper
                                            className="w-full  md:w-[200px] lg:w-[210px] "
                                            name={`conditions.${index}.type3`}
                                            placeholder={"Enter Value"}
                                            disabled={
                                              !values.conditions[index].type1 ||
                                              values.conditions[index].type1 ===
                                                "" ||
                                              !values.conditions[index].type2 ||
                                              values.conditions[index].type2 ===
                                                ""
                                            }
                                          />
                                        )}
                                      </div>
                                      <div className="flex flex-col mb-4">
                                        <InputLabel label={"Operator"} />
                                        <Select
                                          className="w-full md:w-[130px] lg:w-[180px]"
                                          name={`conditions.${index}.type4`}
                                          options={logicalOperator}
                                          placeholder={"Select Logic"}
                                          isDisabled={
                                            !values.conditions[index].type1 ||
                                            values.conditions[index].type1 ===
                                              ""
                                          }
                                        />
                                      </div>

                                      <div className="">
                                        <button
                                          type="button"
                                          className="rounded-2xl w-8 h-8 bg-gray-300 p-2 flex items-center justify-center"
                                          onClick={() => {
                                            if (
                                              values.conditions.length === 1
                                            ) {
                                              setFieldValue(
                                                `conditions.${index}.type1`,
                                                ""
                                              );
                                              setFieldValue(
                                                `conditions.${index}.type2`,
                                                ""
                                              );
                                              setFieldValue(
                                                `conditions.${index}.type3`,
                                                ""
                                              );
                                              setFieldValue(
                                                `conditions.${index}.type4`,
                                                ""
                                              );
                                            } else {
                                              remove(index);
                                              const newOptionDataList = [
                                                ...optionDataList,
                                              ];
                                              newOptionDataList.splice(
                                                index,
                                                1
                                              );
                                              setOptionDataList(
                                                newOptionDataList
                                              );
                                            }
                                          }}
                                        >
                                          <span className="text-xl font-bold">
                                            -
                                          </span>
                                        </button>
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                            <div className="flex-grow flex justify-end">
                              <button
                                type="button"
                                className="w-8 h-8 p-2 "
                                onClick={() => {
                                  const allFieldsFilled =
                                    values.conditions.every(
                                      (condition) =>
                                        condition.type1 &&
                                        condition.type2 &&
                                        condition.type3 &&
                                        condition.type4
                                    );
                                  if (allFieldsFilled) {
                                    push({
                                      type1: "",
                                      type2: "",
                                      type3: "",
                                      type4: "",
                                    });
                                  }
                                }}
                                disabled={
                                  !values.conditions.every(
                                    (condition) =>
                                      condition.type1 &&
                                      condition.type2 &&
                                      condition.type3 &&
                                      condition.type4
                                  ) ||
                                  values.conditions.some(
                                    (condition) => condition.type4 === "no"
                                  )
                                }
                              >
                                <span className="text-sm font-bold">+Add</span>
                              </button>
                            </div>
                          </>
                        );
                      }}
                    />
                  </div>
                </div>
                <div className="flex flex-col lg:flex-row mt-10 mx-12 lg:gap-5 gap-3">
                  <div className="relative md:flex flex-col w-1/2 border border-outerBorder p-3 marker:">
                    <div className="absolute -top-3 px-2 bg-white">
                      <InputLabel label={"X Axis"} />
                    </div>
                    <div className="flex flex-col items-center gap-3 mx-3 my-3">
                      <div className="flex flex-col">
                        <InputLabel label={"Field"} isMandatory={true} />
                        <CustomDropDown
                          btnWidth="w-full lg:w-[400px]"
                          data={field}
                          btnName={"Select Field"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("field", selectedDetail);
                          }}
                          defaultSelectedData={defaultXAxis}
                          isMulti={false}
                        />
                        {errors.field && touched.field && (
                          <div className="text-red-500 text-xs">
                            {errors.field}
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col">
                        <InputLabel label={"No. of records"} />
                        <Select
                          className="w-full lg:w-[400px]"
                          name="records"
                          options={recordsPanel}
                          placeholder={"Select no of records"}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="relative md:flex flex-col w-1/2 border border-outerBorder p-3">
                    <div className="absolute -top-3 px-2 bg-white">
                      <InputLabel label={"Y Axis"} />
                    </div>
                    <div className="flex flex-col items-center gap-3 mx-3 my-3">
                      <div className="flex flex-col">
                        <InputLabel
                          label={"Derived Field"}
                          isMandatory={true}
                        />
                        <CustomDropDown
                          btnWidth="w-full lg:w-[400px]"
                          data={derivedFieldList}
                          btnName={"Select Derived Field"}
                          onSelectionChange={(selectedDetail) => {
                            //console.log("selectedDetail", selectedDetail);
                            setFieldValue("derivedField", selectedDetail);
                          }}
                          defaultSelectedData={defaultDerivedField}
                          optionDataList={configApiData?.MAX_Y_AXIS_FIELDS}
                        />
                        {errors.derivedField && touched.derivedField && (
                          <div className="text-red-500 text-xs">
                            {errors.derivedField}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex-grow flex justify-end items-center mx-20 mt-20 gap-4">
                  <BackButton
                    label={"Back"}
                    buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded"
                    onClick={() => {
                      handlePrevClick();
                      handlePrevStep();
                    }}
                  ></BackButton>
                  <Button
                    block="true"
                    type="submit"
                    label="Next"
                    value="submit"
                    buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded "
                  ></Button>
                </div>
              </Form>
            )}
          </Formik>
        )}
      </div>
      <InfoModal
        show={showAlertConfirmation}
        onHide={() => {
          setShowAlertConfirmation(false);
        }}
        message={message}
        msg={msg}
      />
    </>
  );
}

export default BarChartFormTwo;
