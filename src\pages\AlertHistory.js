import React, { useContext, useMemo, useState, useEffect } from "react";
import Table from "../components/table/Table";
import { SearchIcon, RefreshIcon, ViewIcon, BulkUpdateIcon } from "../icons";
import Button from "../components/Button/OutlinedButton";
import Title from "../Title";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import theme from "../tailwind-theme";
import Calendar from "../components/DatePicker/Calendar";
import dayjs from "dayjs";
import StatusUpdate from "../components/Dropdown/StatusUpdate";
import BulkUpdate from "../popups/BulkUpdate";
import CustomerPopup from "../popups/CustomerPopup";
import { useAlertsHistory } from "../hooks/useAlerts.hooks";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import Pagination from "../components/Pagination/Pagination";
import { DataContext } from "../context/DataContext";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import { formatDateToUTCAndLocal } from "../utils/fileDateFormator";
import DownloadAlertHistory from "../alert-management/DownloadAlertHistory";
import { alertServices, viewAlert } from "../services/alert-api";
import ExportPopup from "../popups/exportpopup";
import { useQuery } from "react-query";
import ViewAlertDialog from "../popups/ViewAlertDialog";
import { CssTooltip } from "../components/StyledComponent";
import BreadcrumbNavigation from "../components/BreadCrumps/BreadCrump";
import { useNavigate } from "react-router-dom";

const statusOptions = [
  { label: "Open", value: "open" },
  { label: "Analyzing", value: "analyzing" },
  { label: "Fixed", value: "fixed" },
  { label: "Closed", value: "closed" },
];

function AlertHistory() {
  // const [searchStr, setSearchStr] = useState("");

  const [searchInput, setSearchInput] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const { resultPerPage } = useContext(DataContext);
  const [limitPerPage, setLimitPerPage] = useState(10);
  const [selectedFilter, setSelectedFilter] = useState({
    startDate: dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
    endDate: dayjs().format("YYYY-MM-DD HH:mm:ss"),
  });
  const [selectedRange, setSelectedRange] = useState("Today");
  const [openDialog, setOpenDialog] = useState(false);
  const [viewClick, setViewClick] = useState(false);
  const [viewData, setViewData] = useState([]);
  const [rowId, setRowId] = useState(0);
  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [downloadModal, setDownloadModal] = useState(false);
  const { setCurrentStep, setFormData, setStepCount } =
    useContext(multiStepFormContext);
  const navigate = useNavigate();

  const fetchParams = {
    page: currentPage,
    limit: limitPerPage,
    search: searchInput,
    startDate: selectedFilter?.startDate,
    endDate: selectedFilter?.endDate,
    status: "",
  };
  const {
    data: alertHistoryList,
    error: alertsHistoryError,
    isLoading: alertsHistoryLoading,
  } = useAlertsHistory({ ...fetchParams, refreshKey });

  useEffect(() => {
    if (alertHistoryList?.data?.length === 0 && openDialog) {
      setOpenDialog(false);
    }
  }, [alertHistoryList?.data, openDialog]);

  const columns = useMemo(
    () => [
      {
        Header: "Alert Name",
        accessor: "name",
        Cell: ({ value }) => <div>{value || "-"}</div>,
      },
      {
        Header: "Alert Type",
        accessor: "alertType",
        Cell: ({ value }) => <div>{value || "-"}</div>,
      },

      {
        Header: "Alert Criteria",
        accessor: "category",
        Cell: ({ value }) => {
          const getStyles = (value) => {
            switch (value?.toLowerCase()) {
              case "major":
                return {
                  color: "#a99d3a",
                  backgroundColor: "#fff8de",
                  fontWeight: "bold",
                };
              case "minor":
                return { color: "#4e53bd", backgroundColor: "#e5e7ff" };
              case "critical":
                return { color: "#e25d59", backgroundColor: "#ffe5e5" };
              default:
                return { color: "#000000", backgroundColor: "#ffffff" };
            }
          };

          const styles = getStyles(value);

          return (
            <div
              style={{
                ...styles,
                padding: "7px 14px",
                borderRadius: "12px",
                display: "inline-block",
              }}
            >
              {value || "-"}
            </div>
          );
        },
      },

      {
        Header: () => (
          <div className="flex items-center">
            <span>Timestamp</span>
          </div>
        ),
        accessor: "timestamp",
        Cell: ({ value }) => (
          <div className="w-full">{formatDateToUTCAndLocal(value).local}</div>
        ),
      },
      {
        Header: () => (
          <div className="flex items-center">
            <span>Status</span>
          </div>
        ),
        accessor: "status",

        Cell: ({ row }) => {
          return (
            <div className="w-full flex flex-col items-start">
              <StatusUpdate
                statusOptions={statusOptions}
                value={row?.original?.status}
                id={row?.original?.id}
              />
            </div>
          );
        },
      },

      {
        Header: "",
        accessor: "action",
        Cell: ({ row }) => (
          <div className="flex justify-center">
            <ViewIcon
              onClick={() => {
                setViewClick(true);
                setRowId(row?.original?.id);
              }}
            />
          </div>
        ),
      },
    ],
    []
  );

  useQuery(["/getAlertsHistory", rowId], viewAlert, {
    enabled: viewClick,
    onSuccess: (res) => {
      setOpenViewDialog(true);
      setViewData(res?.data);
      setViewClick(false);
    },
  });

  function handlePageChange(page) {
    setCurrentPage(page);
  }
  const handleLimitChange = (e) => {
    setLimitPerPage(e?.target?.value);
    setCurrentPage(1);
  };
  // if (alertsHistoryLoading)
  //   return (
  //     <h2 className="w-full h-screen flex justify-center items-center text-center p-6">
  //       Loading...
  //     </h2>
  //   );
  if (alertsHistoryError)
    return (
      <h2 className="w-full h-screen flex justify-center items-center text-center p-6">
        Something went wrong
      </h2>
    );
  return (
    <>
      <div className="bg-bgPrimary my-5">
        <BreadcrumbNavigation
          linkTwo={"List of Alerts"}
          onlinkTwoClick={() => navigate("/app/alerts")}
          title={"Alert History"}
        />
        {/* <Title title={"Alert History"} /> */}

        <div className="border border-listBorder bg-white p-3">
          <div className="mx-3 mt-3">
            <div className="mt-5">
              <div className="mx-3">
                <InputLabel label={"Alert Name"} />
                <div className="w-full flex mt-3">
                  <div className="w-1/2">
                    <input
                      type="text"
                      style={{
                        border: `1px solid ${theme.borderColor.outerBorder}`,
                        paddingLeft: "2rem",
                      }}
                      className="w-full px-4 py-2 text-tabColor bg-white rounded-md focus:outline-none focus:bg-white focus:shadow-outline text-sm font-normal h-10"
                      placeholder="Search Alert"
                      value={searchInput}
                      onChange={(e) => {
                        setSearchInput(e.target.value);
                      }}
                    />
                    <div
                      className="top-0 right-0 mr-4"
                      style={{
                        marginLeft: "0.25rem",
                        marginTop: "-1.7rem",
                      }}
                    >
                      <SearchIcon className="w-5 h-5" />
                    </div>
                  </div>
                  <div className="flex-grow flex justify-end items-center gap-5">
                    <div className="border border-calendarBoreder rounded-md p-2.5 cursor-pointer">
                      <CssTooltip title={"Refresh"} placement="top" arrow>
                        <RefreshIcon
                          onClick={() => {
                            setRefreshKey((prevKey) => prevKey + 1);
                          }}
                        />
                      </CssTooltip>
                    </div>
                    <div className="border border-calendarBoreder rounded-md p-2.5 cursor-pointer">
                      <CssTooltip title={"Bulk Update"} placement="top" arrow>
                        <BulkUpdateIcon
                          onClick={() => {
                            setOpenDialog(true);
                          }}
                        />
                      </CssTooltip>
                    </div>
                    <div>
                      <Calendar
                        setSelectedFilter={setSelectedFilter}
                        setSelectedRange={setSelectedRange}
                        selectedRange={selectedRange}
                      />
                    </div>
                    <Button
                      buttonClassName="text-xs w-36 text-white h-10 rounded-md"
                      label={"Download"}
                      download={true}
                      onClick={() => setDownloadModal(true)}
                    />
                  </div>
                </div>
              </div>
              <div className="mt-3 mx-3">
                <Table
                  columns={columns}
                  data={alertHistoryList?.data ? alertHistoryList?.data : []}
                  isLoading={alertsHistoryLoading}
                />
              </div>
              {alertHistoryList?.data &&
                alertHistoryList?.totalCount !== undefined && (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      margin: "20px 0px 20px 20px",
                      width: "95%",
                    }}
                  >
                    <div className="flex ">
                      <div>
                        <ResultPerPageComponent
                          countPerPage={resultPerPage}
                          limit={limitPerPage}
                          handleLimitChange={handleLimitChange}
                        />
                      </div>

                      <div
                        style={{
                          display: "flex",
                          fontSize: "14px",
                          padding: "10px 0px 0px 10px",
                          color: theme.textColor.titleColor,
                        }}
                      >
                        {alertHistoryList?.totalCount <= 0
                          ? 0
                          : (currentPage - 1) * limitPerPage + 1}{" "}
                        -{" "}
                        {Math.min(
                          limitPerPage * currentPage,
                          alertHistoryList?.totalCount
                        )}{" "}
                        of {alertHistoryList?.totalCount} rows
                      </div>
                    </div>
                    <Pagination
                      className="pagination-bar"
                      currentPage={currentPage}
                      totalCount={alertHistoryList?.totalCount}
                      pageSize={limitPerPage}
                      onPageChange={(page) => {
                        handlePageChange(page);
                      }}
                    />
                  </div>
                )}{" "}
            </div>
          </div>
        </div>
        {openDialog && alertHistoryList?.data?.length > 0 && (
          <BulkUpdate
            openDialog={openDialog}
            closeDialog={() => setOpenDialog(false)}
            selectedFilter={selectedFilter}
            alertHistoryList={alertHistoryList}
          />
        )}

        {downloadModal ? (
          <ExportPopup
            show={downloadModal}
            onHide={() => setDownloadModal(false)}
            onConfirm={(type) => {
              alertServices?.getAlertsHistory({
                ...fetchParams,
                download: true,
                downloadType: type,
              });
              setDownloadModal(false);
            }}
            title={"Export"}
            identity={"Alerts"}
          />
        ) : null}
        {openViewDialog ? (
          <ViewAlertDialog
            open={openViewDialog}
            data={viewData}
            handleClose={() => {
              setOpenViewDialog(false);
            }}
          />
        ) : null}
      </div>
    </>
  );
}

export default AlertHistory;
