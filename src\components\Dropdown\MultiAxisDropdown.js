import React, { useState, useRef, useEffect, memo, useMemo } from "react";
import ExpandMore from "@mui/icons-material/ExpandMore";
import ExpandLess from "@mui/icons-material/ExpandLess";
import SearchIcon from "@mui/icons-material/Search";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import { useTranslation } from "react-i18next";
import { CircularProgress } from "@mui/material";
import { VariableSizeList as List, areEqual } from "react-window";
import { Select, MenuItem } from "@mui/material";
import { CloseIcon, BarIcon, LineIcon } from "../../icons";

import { twMerge } from "tailwind-merge";
import { object } from "joi";

const MultiAxisDropdown = ({
  isEdit,
  btnName,
  btnWidth,
  btnHeight,
  width,
  data,
  isLoading,
  reset,
  onSelectionChange,
  isMulti = true,
  disabled = false,
  defaultSelectedData,
  commonValues,
  isSearch = true,
  searchParameter = "value",
  isReports,
}) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [searchText, setSearchText] = useState("");
  const buttonRef = useRef(null);
  const [isSelecting, setIsSelecting] = useState(false);

  // const Styles = {
  //   btnPrimary: `bg-transparent text-gray-400 px-2 w-full border flex items-center rounded-[5px] ${
  //     btnWidth ? btnWidth : "min-w-[526px]"
  //   } ${btnHeight ? btnHeight : "h-[40px]"}`,
  // };
  const Styles = {
    btnPrimary: `bg-transparent text-gray-400 px-2 w-full border border-outerBorder flex min-h-[40px] items-center rounded-[5px] ${
      btnWidth ? btnWidth : "min-w-[400px]"
    } ${btnHeight ? btnHeight : "h-full"}`, //"h-[40px]" flex-wrap
  };
  const itemHeight = 30;
  const maxListHeight = 150;

  const options = [
    { label: "Line", value: "Line Graph" },
    { label: "Bar", value: "Bar Graph" },
  ];
  const [selectedValue, setSelectedValue] = useState(options[0].value);
  const [selectObject, setSelectObject] = useState({});
  const [enabledList, setEnabledList] = useState([]);

  const handleOpen = (e) => {
    e.preventDefault();
    setOpen(!open);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    // debugger;
    // console.log("error-check-useeffect");

    // Event listener to handle clicks outside of the dropdown
    const handleClickOutside = (event) => {
      // Check if the click target is inside the dropdown button or its children
      if (
        buttonRef.current &&
        !buttonRef.current.contains(event.target) &&
        !event.target.classList.contains("custom-checkbox") && // Example class for checkbox
        !event.target.classList.contains("custom-select") &&
        !event.target.classList.contains("custom-search") // Example class for search component
      ) {
        setOpen(false);
      }
    };

    // Attach the event listener to the document
    document.addEventListener("mousedown", handleClickOutside);

    // Remove the event listener when the component is unmounted or re-rendered
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // useEffect(() => {
  //   // console.log("commonValues", commonValues);
  //   if (commonValues) {
  //     const valuesArray = Array.isArray(commonValues)
  //       ? commonValues
  //       : commonValues.split(",");
  //     setSelectedOptions(valuesArray);
  //   } else {
  //     setSelectedOptions([]);
  //   }
  // }, [commonValues]);

  useEffect(() => {
    // console.log("error-check", commonValues);
    if (commonValues) {
      const valuesArray = Array.isArray(commonValues)
        ? commonValues
        : commonValues.split(",");
      setSelectedOptions(valuesArray);
      // Update input value whenever selected options change
      // setInputValue(valuesArray.join(", "));
    } else {
      setSelectedOptions([]);
      //  setInputValue("");
    }
  }, [commonValues]);

  const handleCheckboxChange = ({ target }, option) => {
    // console.log("###selectedValue->", option.value, target.checked);
    setEnabledList((oldState) => {
      let newState = [...oldState];
      if (target.checked) {
        newState.push(option.value);
        setSelectObject((oldState) => {
          let newState = { ...oldState };
          newState[option.value] = "Line Graph";
          return newState;
        });
      } else {
        newState = newState.filter((val) => val !== option.value);
        setSelectObject((oldState) => {
          let newState = { ...oldState };
          delete newState[option.value];
          return newState;
        });
      }
      return newState;
    });
    if (!isMulti) {
      if (selectedOptions.includes(option.value)) {
        setSelectedOptions([]);
      } else {
        setSelectedOptions([option.value]);
        // setSelectedValue(selectedValue);
      }
    } else {
      //  setSelectedValue(selectedValue);
      setSelectedOptions((prevSelectedOptions) => {
        if (prevSelectedOptions.includes(option.value)) {
          return prevSelectedOptions.filter(
            (selectedOption) => selectedOption !== option.value
          );
        } else {
          return [...prevSelectedOptions, option.value];
        }
      });
    }
  };

  const handleSearchChange = (event) => {
    setSearchText(event.target.value);
  };
  useEffect(() => {
    //console.log("error-check", reset);
    setSelectedOptions([]);
    setSearchText("");
  }, [reset]);

  useEffect(() => {
    //console.log("error-check", selectObject);

    // console.log("selectedDetail-selectObject", selectObject);
    onSelectionChange(selectObject);
  }, [selectObject]);

  // useEffect(() => {
  //   if (defaultSelectedData) {
  //     setSelectedOptions(defaultSelectedData);
  //     let tempobj = {};
  //     // const tempobj = defaultSelectedData.reduce((acc, { name, type }) => {
  //     //   acc[name] = type;
  //     //   return acc;
  //     // }, {});
  //     defaultSelectedData?.forEach(({ name, type }) => (tempobj[name] = type));
  //     setSelectObject(tempobj);
  //     setEnabledList(Object.keys(tempobj));
  //     console.log("error-check", defaultSelectedData, "tempobj", tempobj);
  //   }
  // }, [defaultSelectedData]);
  useEffect(() => {
    if (defaultSelectedData) {
      setSelectedOptions(defaultSelectedData.map((item) => item.name)); // Extracting names from objects
      let tempobj = {};
      defaultSelectedData.forEach(({ name, type }) => (tempobj[name] = type)); // Assuming `name` as the key and `type` as the value
      setSelectObject(tempobj);
      setEnabledList(Object.keys(tempobj));
      //console.log("error-check", defaultSelectedData, "tempobj", tempobj);
    }
  }, [defaultSelectedData]);

  const removeSelectedOption = (e, optionValue) => {
    e.preventDefault();
    e.stopPropagation();
    setSelectedOptions((prevSelectedOptions) =>
      prevSelectedOptions.filter(
        (selectedOption) => selectedOption !== optionValue
      )
    );
    setEnabledList((prevSelectedOptions) =>
      prevSelectedOptions.filter(
        (selectedOption) => selectedOption !== optionValue
      )
    );

    setSelectObject((prevSelectObject) => {
      // Create a copy of the previous selectObject
      const newSelectObject = { ...prevSelectObject };
      // Add or update the optionValue with "Line Graph"
      newSelectObject[optionValue] = "Line Graph";
      return newSelectObject;
    });
  };

  const combinedOptions = useMemo(() => {
    const filteredOptions =
      data?.length > 0
        ? data.filter((option) =>
            String(option[searchParameter])
              .toLowerCase()
              .includes(searchText.toLowerCase())
          )
        : [];
    return [
      ...selectedOptions
        .map((selectedOption) =>
          data?.find((option) => option.value === selectedOption)
        )
        .filter(Boolean),
      ...filteredOptions.filter((option) => {
        return (
          option &&
          option?.value &&
          option?.label &&
          !selectedOptions.includes(option?.value)
        );
      }),
    ];
  }, [data, selectedOptions, searchText, searchParameter]);

  const Row = memo(({ data, index, style }) => {
    const handleChange = (e) => {
      const { name, value } = e.target;
      setIsSelecting(true);
      setSelectObject((oldState) => ({ ...oldState, [name]: value }));
    };
    const currentOption = combinedOptions?.[index];
    const rowStyle = {
      ...style,
      borderBottom: "1px solid #E5E7EB",
      paddingBottom: "6px",
      marginBottom: "6px",
      paddingTop: "2px",
    };

    const handleBlur = () => {
      setIsSelecting(false);
    };

    // console.log(
    //   "error-check",
    //   enabledList,
    //   selectedOptions,
    //   selectObject,
    //   combinedOptions
    // );

    return (
      <div key={index} style={rowStyle}>
        <label className="flex justify-between gap-2 text-xs">
          <div className="text-xs pt-2">{currentOption?.label}</div>
          <div className="flex-grow flex items-center justify-end ">
            {/* {console.log("selectObject", selectObject[currentOption?.label])} */}
            <select
              name={currentOption?.label}
              value={selectObject[currentOption?.label]}
              onChange={(e) => handleChange(e)}
              // className="w-[80px] h-6 text-xs border-2 border-grey custom-select mr-8 "
              className={`w-[80px] h-6 text-xs border-2 border-grey rounded custom-select mr-8 ${
                !enabledList.includes(currentOption?.label)
                  ? "bg-gray-200 cursor-not-allowed"
                  : ""
              }`}
              disabled={!enabledList.includes(currentOption?.label)}
            >
              {options.map((option, index) => (
                <option
                  key={index}
                  value={option.value}
                  // onClick={() => console.log("###clicked")}
                >
                  {option.label}
                </option>
              ))}
            </select>
            {/* </div> */}
            {/* <div className="flex-grow flex justify-end bg-pink-400 mr-3"> */}
            <input
              type="checkbox"
              checked={enabledList.includes(currentOption?.value)}
              onChange={(e) => handleCheckboxChange(e, currentOption)}
              className="min-w-[20px] w-4 h-4 custom-checkbox"
              style={{ accentColor: "#707070" }}
            />
          </div>
        </label>
      </div>
    );
  }, areEqual);

  return (
    <div
    //   onClickAway={
    //     isSelecting
    //       ? () => {
    //           setOpen(true);
    //         }
    //       : handleClose
    //   }
    >
      <div
        className={` ${btnWidth ? "" : "min-w-[150px]"}  relative text-medium`}
      >
        <button
          ref={buttonRef}
          onClick={handleOpen}
          // className={
          //   open ? `${Styles.btnPrimary} border-gray-800` : Styles.btnPrimary
          // }
          className={
            open
              ? `flex  ${Styles.btnPrimary} border-outerBorder`
              : Styles.btnPrimary
          }
          disabled={disabled}
        >
          {/* <div className="flex justify-between w-full whitespace-nowrap">
            <div className="text-xs mt-1">
              {t(btnName)}{" "}
              {selectedOptions.length ? (
                <span>({selectedOptions.length})</span>
              ) : null}
            </div>
            <div className="flex items-center ml-5">
              {open ? (
                <ExpandLess className="ml-2" />
              ) : (
                <ExpandMore className="ml-2" />
              )}
            </div>
          </div> */}
          <div className="flex w-11/12 flex-wrap  ">
            <div className="text-xs mt-1 flex flex-wrap  ">
              {selectedOptions.map((option, index) => (
                <span
                  key={index}
                  className="flex text-black items-center m-1 rounded h-6 px-2 bg-bgTeritary"
                >
                  <span>{option}</span>
                  {console.log("selectObject", selectObject, option)}
                  {selectObject.hasOwnProperty(option) &&
                  selectObject[option].includes("Bar Graph") ? (
                    <BarIcon
                      // onClick={(e) => removeSelectedOption(e, option)}
                      className="h-2.5 w-2.5 ml-2.5"
                    />
                  ) : (
                    <LineIcon
                      // onClick={(e) => removeSelectedOption(e, option)}
                      className="h-2.5 w-2.5 ml-2.5"
                    />
                  )}
                  <CloseIcon
                    onClick={(e) => removeSelectedOption(e, option)}
                    className="h-1.5 ml-1.5"
                  />
                </span>
              ))}
            </div>
          </div>
          <div>
            {/* //flex items-center  */}
            {open ? (
              <ExpandLess className="ml-2" />
            ) : (
              <ExpandMore className="ml-2" />
            )}
          </div>
        </button>
        {open ? (
          <div className="relative bottom-0 left-0 z-50 ">
            <div
              className={`bg-white shadow-md p-4 rounded absolute   ${
                btnWidth ? "min-w-full" : "min-w-[150px]"
              }`}
            >
              {isSearch && (
                <div className="mb-4 ">
                  <div
                    className={`flex items-center border border-gray-300 px-2 py-1  w-full `}
                  >
                    <SearchIcon className="text-gray-400 mr-4 ml-1" />
                    <input
                      type="text"
                      placeholder="Search"
                      value={searchText}
                      onChange={handleSearchChange}
                      className="border-none outline-none w-full text-xs custom-search"
                    />
                  </div>
                </div>
              )}
              {isLoading ? (
                <div className="flex justify-center">
                  <CircularProgress />
                </div>
              ) : (
                <div className="w-full max-h-72 ">
                  <List
                    width={width}
                    height={Math.min(
                      combinedOptions.length * itemHeight,
                      maxListHeight
                    )}
                    itemCount={combinedOptions?.length}
                    itemData={combinedOptions}
                    itemSize={(index) =>
                      combinedOptions[index]?.label?.length > 150 ? 75 : 30
                    }
                  >
                    {Row}
                  </List>
                </div>
              )}
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default MultiAxisDropdown;
