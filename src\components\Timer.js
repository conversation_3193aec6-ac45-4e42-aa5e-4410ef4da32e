import { useState } from "react";
import { DownArrowIcon, UpArrowIcon } from "../icons";
import * as Yup from "yup";
import dayjs from "dayjs";
import { formatDate } from "../utils/fileDateFormator";

export const TimeComponent = (props) => {
  const [time, setTime] = useState({
    hours: props.values?.[props.name]?.["hours"],
    minutes: props.values?.[props.name]?.["minutes"],
    seconds: props.values?.[props.name]?.["seconds"],
  });

  const updateBoth = (field, value) => {
    const updatedTime = {
      ...props.values[props.name],
      [field]: value,
    };

    // Update Formik field
    props.setFieldValue(props.name, updatedTime);

    // Also update local timeDate if provided
    props.setTimeDate?.((prev) => ({
      ...prev,
      [props.name]: {
        ...prev[props.name],
        [field]: value,
      },
    }));
  };

  const handleTimeChange = (field, time, setTime) => (e) => {
    const { value } = e.target;
    let numericValue = value.replace(/\D/g, "");

    if (field === "hours" && parseInt(numericValue) > 23) {
      numericValue = "23";
    } else if (field === "minutes" && parseInt(numericValue) > 59) {
      numericValue = "59";
    }

    setTime((prevTime) => ({
      ...prevTime,
      [field]: numericValue,
    }));

    updateBoth(field, numericValue);
  };

  const handleIncrement = (field, time, setTime, limit) => () => {
    setTime((prevTime) => {
      const newValue = (parseInt(prevTime[field], 10) + 1) % (limit + 1);
      const formatted = newValue < 10 ? `0${newValue}` : `${newValue}`;
      updateBoth(field, formatted);
      return { ...prevTime, [field]: formatted };
    });
  };

  const handleDecrement = (field, time, setTime, limit) => () => {
    setTime((prevTime) => {
      const newValue = Math.max(
        (parseInt(prevTime[field], 10) - 1 + (limit + 1)) % (limit + 1),
        0
      );
      const formatted = newValue < 10 ? `0${newValue}` : `${newValue}`;
      updateBoth(field, formatted);
      return { ...prevTime, [field]: formatted };
    });
  };

  return (
    <div className="flex items-center flex-col mt-5 px-5">
      <div className="text-titleColor text-xs font-['Open Sans Hebrew'] whitespace-nowrap font-bold items-center">
        {props.label}
      </div>
      <div className="flex flex-row">
        {["hours", "minutes"].map((field) => (
          <div className="flex flex-col" key={`${props.name}-${field}`}>
            <div className="flex items-center">
              <div className="px-1 h-7 border border-listBorder rounded-md text-[#707070] flex">
                <input
                  name={field}
                  value={time[field]}
                  onChange={handleTimeChange(field, time, setTime)}
                  maxLength="2"
                  className="focus:outline-none w-5"
                />
              </div>
              <div className="ml-1 flex flex-col gap-1">
                <div
                  className="border border-listBorder h-2 flex items-center justify-center cursor-pointer"
                  onClick={handleIncrement(
                    field,
                    time,
                    setTime,
                    field === "hours" ? 23 : 59
                  )}
                >
                  <UpArrowIcon />
                </div>
                <div
                  className="border border-listBorder h-2 flex items-center justify-center cursor-pointer"
                  onClick={handleDecrement(
                    field,
                    time,
                    setTime,
                    field === "hours" ? 23 : 59
                  )}
                >
                  <DownArrowIcon />
                </div>
              </div>
            </div>
            <div className="text-xs text-[#707070] text-center">
              {field === "hours" ? "hh" : "mm"}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const CDRvalidation = (configApiData, isAdmin) =>
  Yup.object().shape({
    aNumber: Yup.string().max(20, "Max of 20 characters allowed"),

    bNumber: Yup.string().max(20, "Max of 20 characters allowed"),

    messageId: Yup.number()
      .typeError("Only numbers allowed")
      .max(99999999999999999, "Max of 17 digits allowed"),

    destMcc: Yup.number()
      .typeError("Destination MCC must be a number")
      .integer("Destination MCC must be an integer")
      .max(999, "Destination MCC must be a 3-digit code")
      .notOneOf([0], "Destination MCC cannot be 000"),

    destMnc: Yup.number()
      .typeError("Destination MNC must be a number")
      .integer("Destination MNC must be an integer")
      .max(999, "Destination MNC must be between 1 and 3 digits"),

    customerName: isAdmin
      ? Yup.array().of(Yup.string())
      : Yup.array()
          .of(Yup.string())
          .min(1, "Customer Name is required")
          .required("Customer Name is required"),
    supplierName: isAdmin
      ? Yup.array().of(Yup.string())
      : Yup.array()
          .of(Yup.string())
          .min(1, "Supplier Name is required")
          .required("Supplier Name is required"),

    selectedDate: Yup.array()
      .required("Date is required")
      .test("is-valid-range", "", function (val) {
        const { startTime, endTime } = this.parent;

        const startDateTime =
          dayjs(val?.[0]).format("YYYY/MM/DD") +
          " " +
          startTime?.hours +
          ":" +
          startTime?.minutes +
          ":00";

        const endDateTime =
          dayjs(val?.[1] ?? val?.[0]).format("YYYY/MM/DD") +
          " " +
          endTime?.hours +
          ":" +
          endTime?.minutes +
          ":00";

        const diff = dayjs(endDateTime).diff(dayjs(startDateTime), "m");

        if (diff < 0) {
          return this.createError({
            path: "selectedDate",
            message: "Please select a valid start and end date time",
          });
        }

        if (diff > configApiData.MAX_TIME_RANGE_RAW_CDR) {
          return this.createError({
            path: "selectedDate",
            message: `Time range should not exceed ${configApiData.MAX_TIME_RANGE_RAW_CDR} mins`,
          });
        }

        return true;
      }),

    transactionType: Yup.array().min(
      1,
      "At least one transaction flow type is required"
    ),
  });

export const getFieldName = (x) => {
  let data = {
    aNumber: "a_number",
    bNumber: "b_number",
    destMcc: "destination_mcc_final",
    destMnc: "destination_mnc_final",
    destinationName: "destination",
    destinationCountry: "destination_country_name",
    customerName: "customer_name",
    interfaceType: "supplier_interface_type",
    messageId: "event_id",
    protocolType: "traffic_type",
    supplierName: "supplier",
    customerBind: "customer_bind",
    supplierBind: "supplier_bind",
    transactionType: "transaction_type",
  };
  return data[x];
};
export const buildFilterData = (values) => {
  return Object.keys(values).reduce((acc, key) => {
    const field = getFieldName(key);
    if (values[key] && field) {
      let filteredValue = Array.isArray(values[key])
        ? values[key].filter((item) => item !== "Select All" && item !== "All")
        : values[key];

      // Special handling for Transaction Type field
      if (field === "transaction_type" && Array.isArray(filteredValue)) {
        // Process each element in the array that may contain comma-separated values
        const expandedValues = filteredValue.flatMap((item) => {
          // Split each item by comma and trim whitespace, then wrap each value in its own array
          return item.split(",").map((val) => val.trim());
        });
        filteredValue = expandedValues.flat(); // Flatten the array
      }

      if (
        filteredValue &&
        (!Array.isArray(filteredValue) || filteredValue.length > 0)
      ) {
        acc[field] = filteredValue; // Use field as the key instead of pushing an object
      }
    }
    return acc;
  }, {});
};

export const getFormattedDateTimeRange = (values) => {
  const startDate = values?.selectedDate?.[0];
  const endDate = values?.selectedDate?.[1] ?? startDate;

  const start = dayjs(startDate)
    .hour(parseInt(values.startTime.hours))
    .minute(parseInt(values.startTime.minutes));
  const end = dayjs(endDate)
    .hour(parseInt(values.endTime.hours))
    .minute(parseInt(values.endTime.minutes));

  return {
    startFormatted: start.format("YYYY-MM-DD HH:mm:ss"),
    endFormatted: end.format("YYYY-MM-DD HH:mm:ss"),
    timeDifferenceInHours: (new Date(end) - new Date(start)) / (1000 * 60 * 60),
  };
};

export const generateFilename = (filters) => {
  const { startDate, endDate } = filters;

  const start = dayjs(startDate).format("YYYY-MM-DD");
  const end = dayjs(endDate).format("YYYY-MM-DD");

  const downloadTime = dayjs().format("HH_mm_ss");

  let filename = `CDR_${start}_${end}_${downloadTime}_${`multiselect`}.csv.zip`;

  if (filename.length > 255) {
    filename = filename.slice(0, 251) + ".csv.zip";
  }

  return filename;
};
