import React, { useState, useContext, useEffect } from "react";
import {
  getDashboardById,
  updateDashboard,
} from "../../services/dashboard-api";
import { useQuery, useMutation } from "react-query";
import { previewPanel, getId } from "../../services/panels-api";
import ResponsiveLayout from "./ResponsiveLayout";
import { useParams, useNavigate } from "react-router-dom";
import BackButton from "../../components/Button/Button";
import PanelPreviewData from "./PanelPreviewData";
import AddCard from "../../popups/AddCard";
import CardPreviewData from "./CardPreviewData";
import RemoveCard from "../../popups/RemoveCard";
import AvailableItemsAccordion from "../../components/Accordion/AvailableItemsAccordion";
import Button from "../../components/Button/OutlinedButton";
import CancelButton from "../../components/Button/Button";
import SuccessDialog from "../../popups/SuccessDialog";
import ErrorDialog from "../../popups/ErrorDialog";
import { AuthContext } from "../../context/AuthContext";
import { multiStepFormContext } from "../../context/MultiStepFormContext";
import InfoModal from "../../components/modals/InfoModal";
import BreadcrumbNavigation from "../../components/BreadCrumps/BreadCrump";
import { EditingIcon } from "../../icons";
import dayjs from "dayjs";
import { convertToUTC } from "../../common/commonFunctions";
import customParseFormat from "dayjs/plugin/customParseFormat";
import ResponsiveLayoutCard from "./ResponsiveLayoutCard";

function DashboradEdit() {
  const parms = useParams();
  const [openDialog, setOpenDialog] = useState(false);
  const [removeDialog, setRemoveDialog] = useState(false);
  const [cardDroppedData, setCardDroppedData] = useState([]);
  const [previewResponses, setPreviewResponses] = useState([]);
  const [message, setMessage] = useState(false);
  const [message1, setMessage1] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [isCardAdded, setIsCardAdded] = useState(false);
  const [editDashboardName, setEditDashboardName] = useState(false);
  const [dashboardNameErr, setDashboardNameErr] = useState();
  const [panelId, setPanelId] = useState("");
  const [loading, setLoading] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);

  const { configApiData } = useContext(AuthContext);
  const [totalCount, setTotalCount] = useState({
    panel: 0,
    card: 0,
  });
  dayjs.extend(customParseFormat);
  const navigate = useNavigate();
  const value = parms.id;

  const { roles } = useContext(AuthContext);
  //console.log("roles", roles);

  const permissions = roles.resources.filter(
    (res) => res.name === "Card Management"
  )[0].permissions;

  const panelPermissions = roles.resources.filter(
    (res) => res.name === "Panel Management"
  )[0].permissions;

  const { setCurrentStep, setStepCount, setFormData } =
    useContext(multiStepFormContext);

  //const { data: dataList } = useQuery(["dashboar  dList"], getAll);
  function openCard() {
    setOpenDialog(true);
  }
  function closeCard() {
    setOpenDialog(false);
  }

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleCardAdded = () => {
    setIsCardAdded(true);
  };

  const handleCardAddedFalse = () => {
    setIsCardAdded(false);
  };

  const {
    data: dashboardList,
    isLoading: isloading,
    isFetching,
  } = useQuery(
    ["getDashboardById", value],

    getDashboardById,
    {
      onSuccess: ({ data }) => {
        setCardDroppedData(
          data?.cards.map((card) => ({
            reportField: card.cardDetails.reportField,
            value: card.cardDetails.value,
            order: card.order,
            id: card.cardDetails.id,
          }))
        );
        const panels = data?.panels?.map((panel, i) => {
          let dimension = {
            w: panel.order > 1 ? 3 : 6,
            h: 3,
            x: 0,
            y: 0,
            order: panel.order,
          };

          return { ...panel, dimension };
        });
        setPreviewResponses(panels);
        previewAPICall(panels);
      },
      refetchOnWindowFocus: false,
    }
  );
  useQuery(["getPanelById", panelId], getId, {
    enabled: !!panelId,
    onSuccess: ({ data }) => {
      //console.log("getPanelById", data);
      const dimension = { w: previewResponses.length > 0 ? 3 : 6, h: 3 };
      const droppedIndex = previewResponses.findIndex((item) => {
        return item.panelData.id === (data.id ? data?.id : null);
      });
      const dim = getGridData(droppedIndex, dimension);
      const order = previewResponses.length + 1;
      previewAPICall([
        {
          panelDetails: data,
          dimension: {
            ...dim,
            order: order,
          },
        },
      ]);
      setLoading(true);
      //previewById(data);
    },
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (loading) {
      //console.log("abc");
      const dimension = { w: previewResponses.length > 0 ? 3 : 6, h: 3 };
      const order = previewResponses.length + 1;
      setPreviewResponses((prevResponses) => {
        const dummyEntry = {
          data: {},
          panelData: {},
          dimension: dimension,
          order: order,
          id: "dummy",
          timePeriod: "",
          count: 0,
        };
        return [...prevResponses, dummyEntry];
      });
    }
  }, [loading]);

  const [dashboardName, setDashboardName] = useState(dashboardList?.data?.name);

  const previewAPICall = (previewData) => {
    //console.log("previewData", previewData);
    previewData.forEach((payload, index) => {
      //console.log("payload", payload);
      const selectedRange = payload.panelDetails.timePeriod;
      let formattedStart = "";
      let formattedEnd = "";
      const currentDateTime = dayjs();

      if (selectedRange) {
        if (selectedRange.includes("to")) {
          const [startString, endString] = selectedRange.split("to");
          formattedStart = startString;
          formattedEnd = endString;
        } else {
          if (
            selectedRange === "Last Hour" ||
            selectedRange === "Last 6 Hours" ||
            selectedRange === "Last 12 Hours" ||
            selectedRange === "Last 24 Hours"
          ) {
            const hours = {
              "Last Hour": 1,
              "Last 6 Hours": 6,
              "Last 12 Hours": 12,
              "Last 24 Hours": 24,
            };

            const lastXHours = currentDateTime.subtract(
              hours[selectedRange],
              "hour"
            );
            if (selectedRange === "Last Hour") {
              formattedStart = lastXHours.format("YYYY-MM-DD HH:mm:ss");
              formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
            } else {
              formattedStart = lastXHours.format("YYYY-MM-DD HH:00:00");
              formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
            }
          } else if (selectedRange === "Today") {
            formattedStart = currentDateTime
              .startOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
          } else if (selectedRange === "Yesterday") {
            const yesterday = currentDateTime.subtract(1, "day");
            formattedStart = yesterday
              .startOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss");
          } else if (selectedRange === "Last Seven Days") {
            formattedStart = currentDateTime
              .subtract(6, "days")
              .startOf("day")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "Last Week") {
            formattedStart = currentDateTime
              .subtract(1, "week")
              .startOf("week")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .subtract(1, "week")
              .endOf("week")
              .format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "Last 30 Days") {
            formattedStart = currentDateTime
              .subtract(29, "days")
              .startOf("day")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .endOf("day")
              .format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "Last Month") {
            formattedStart = currentDateTime
              .subtract(1, "month")
              .startOf("month")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .subtract(1, "month")
              .endOf("month")
              .format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "This Month") {
            formattedStart = currentDateTime
              .startOf("month")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime
              .endOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
          }
        }
      }

      let reqData = {
        name: payload.panelDetails.name,
        visualizationType: payload.panelDetails.visualizationType,
        filters: [],
        dataColumns: {
          derivedFields: payload.panelDetails.dataColumns?.derivedFields,
        },
        // startDate: convertToUTC(formattedStart),
        // endDate: convertToUTC(formattedEnd),
        startDate: formattedStart,
        endDate: formattedEnd,
      };
      let reqDataForDashBoard = {
        name: payload.panelDetails.name,
        visualizationType: payload.panelDetails.visualizationType,
        filters: [],
        dataColumns: {
          derivedFields: payload.panelDetails.dataColumns?.derivedFields,
        },
        startDate: formattedStart,
        endDate: formattedEnd,
      };
      payload.panelDetails.filters.forEach((condition) => {
        reqDataForDashBoard.filters.push({
          field: condition.field,
          condition: condition.condition,
          value: condition.value,
          operator: condition.operator,
        });
      });
      if (payload.panelDetails.visualizationType === "Bar Graph") {
        reqData.dataColumns["X-Axis"] =
          payload.panelDetails.dataColumns?.["X-Axis"];
        reqData.dataColumns.noOfRecords =
          payload.panelDetails.dataColumns.noOfRecords;
      } else {
        reqData.dataColumns.tableFields =
          payload.panelDetails.dataColumns?.tableFields;
      }
      if (payload.panelDetails.visualizationType === "Bar Graph") {
        reqDataForDashBoard.dataColumns["X-Axis"] =
          payload.panelDetails.dataColumns?.["X-Axis"];
        reqDataForDashBoard.dataColumns.noOfRecords =
          payload.panelDetails.dataColumns.noOfRecords;
      } else {
        reqDataForDashBoard.dataColumns.tableFields =
          payload.panelDetails.dataColumns?.tableFields;
      }
      payload.panelDetails.filters.forEach((condition) => {
        reqData.filters.push({
          field: condition.field,
          condition: condition.condition,
          value: condition.value,
          operator: condition.operator,
        });
      });

      previewPanel({ reqData })
        .then(({ data }) => {
          setLoading(false);
          setPreviewResponses((prevResponses) => {
            let newData = prevResponses
              .filter(({ id }) => id !== "dummy")
              .filter(
                ({ dimension }) => dimension.order !== payload.dimension.order
              );

            newData.push({
              data: data.data,
              panelData: {
                ...reqDataForDashBoard,
                data:
                  reqDataForDashBoard.visualizationType === "MultiAxis Graph"
                    ? data
                    : data?.data,
              },
              dimension: payload.dimension,
              id: payload.panelDetails.id,
              timePeriod: payload.panelDetails.timePeriod,
              count: data?.count,
            });

            return [...newData];
          });
        })

        .catch(() => {
          setLoading(false);
          setPreviewResponses((prevResponses) => {
            let newData = prevResponses
              .filter(({ id }) => id !== "dummy")
              .filter(
                ({ dimension }) => dimension.order !== payload.dimension.order
              );

            newData.push({
              data: [],
              panelData: {
                ...reqDataForDashBoard,
                data: "failed",
              },
              dimension: payload.dimension,
              id: payload.panelDetails.id,
              timePeriod: payload.panelDetails.timePeriod,
            });

            return [...newData];
          });
        });
    });
  };
  const { mutate: updateDashboardAPI, isLoading: updateLoading } =
    useMutation(updateDashboard);

  const updateAPICall = () => {
    const cardIds = cardDroppedData.map((card) => card.id);
    const panelIds = previewResponses.map((panel) => panel.id);

    let reqData = {
      name: dashboardName,
      cards: cardIds.map((id, index) => ({ id, order: index + 1 })),
      panels: panelIds.map((id, index) => ({ id, order: index + 1 })),
    };

    updateDashboardAPI(
      {
        value,
        reqData,
      },
      {
        onSuccess: (resp) => {
          setSuccessDialog(true);
          setMessage("Dashboard updated successfully");
        },
        onError: (error) => {
          //console.log("save response", error);
        },
      }
    );
  };
  const getGridData = (index, dimension) => {
    let dim = { x: 0, y: 0, w: dimension.w, h: dimension.h };
    //console.log("index", index);
    //console.log("index", dimension.order);
    if (dimension.order) {
      switch (dimension.order - 1) {
        case 0:
          return dim;
        default:
          dim.x = (dimension.order - 1) % 2 === 0 ? 6 : 0;
          dim.y = (dimension.order - 1) % 2 === 0 ? 6 : 3;
          return dim;
      }
    } else {
      switch (index) {
        case 0:
          return dim;
        default:
          dim.x = index % 2 === 0 ? 6 : 0;
          dim.y = index % 2 === 0 ? 6 : 3;
          return dim;
      }
    }
  };

  const handleDrop = (e) => {
    try {
      const droppedData = JSON.parse(e.dataTransfer.getData("data"));
      const addPanel = () => {
        const panelIdData = droppedData.data;

        const maxAllowedPanels = roles?.isSuperAdminRole
          ? configApiData?.MAX_PANELS_PER_DASHBOARD
          : roles?.panelCount;

        const isPanelAlreadyDropped = previewResponses.some((panel) => {
          return panel.id === panelIdData;
        });

        if (!isPanelAlreadyDropped) {
          if (previewResponses.length < maxAllowedPanels) {
            setPanelId(droppedData.data);
          } else {
            setShowAlertConfirmation(true);
            setMessage(
              `Maximum ${maxAllowedPanels} panels allowed in a dashboard`
            );
          }
        } else {
          setShowAlertConfirmation(true);
          setMessage("Already the panel is added");
        }
      };
      if (droppedData.type === "panel") {
        addPanel();
      } else if (droppedData.type === "card") {
        const cardData = droppedData.data;
        if (cardData) {
          const cardId = cardData.id;
          const cardExists = cardDroppedData.some((card) => card.id === cardId);
          const maxCardsAllowed = roles.isSuperAdminRole
            ? configApiData?.MAX_CARDS_PER_DASHBOARD
            : 4;

          if (!cardExists) {
            if (cardDroppedData.length < maxCardsAllowed) {
              setCardDroppedData((prevData) => [...prevData, cardData]);
            } else {
              setShowAlertConfirmation(true);
              setMessage(
                `Maximum ${maxCardsAllowed} cards allowed in a dashboard`
              );
            }
          } else {
            setShowAlertConfirmation(true);
            setMessage("Card already exists in the dashboard");
          }
        }
      }
    } catch (error) {
      e.preventDefault();
    }
  };

  const removeCard = (index) => {
    setCardDroppedData((prevData) => prevData.filter((_, i) => i !== index));
  };
  const removePanel = (index) => {
    setPreviewResponses((prevData) => {
      const newData = prevData.filter((_, i) => i !== index);
      return newData.map((ele, i) => {
        let dimension = { w: i > 0 ? 3 : 6, h: 3, x: 0, y: 0 };
        return { ...ele, dimension };
      });
    });
    setPanelId("");
  };

  const handleDashboardNameError = (e) => {
    setDashboardName(e.target.value);
    if (
      !/^(?=.*[a-zA-Z])[a-zA-Z0-9_\s!@#$%^&*()\-+=~`'<>.,/?;:{}[\]|\\]*$/.test(
        e.target.value
      )
    ) {
      setDashboardNameErr("Enter atleast one alphabet");
    } else if (e.target.value?.length < 2) {
      setDashboardNameErr("Min length allowed is 2 characters");
    } else if (e.target.value?.length > 256) {
      setDashboardNameErr("Max length allowed is 256");
    } else {
      setDashboardNameErr("");
    }
  };

  const handleEditName = () => {
    setEditDashboardName(true);
    setDashboardName(dashboardList?.data?.name);
  };

  return (
    <>
      {isloading || isFetching ? (
        <h1 className="w-full h-screen flex justify-center items-center text-center p-6">
          {" "}
          Loading ...{" "}
        </h1>
      ) : (
        <>
          {" "}
          <div
            className="flex mt-10 h-[95%] w-full"
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e)}
          >
            <div>
              <div className=" cursor-pointer">
                <BreadcrumbNavigation
                  //linkOne={"Roles"}
                  linkTwo={"Dashboard"}
                  onlinkTwoClick={() => navigate("/app/dashboard/details")}
                  title={
                    dashboardList?.data?.name
                      ? dashboardList?.data?.name
                      : dashboardName
                  }
                />
                <div className="mt-6">
                  {!editDashboardName ? (
                    <div className=" flex">
                      <p>
                        {dashboardList?.data?.name
                          ? dashboardList?.data?.name
                          : dashboardName}
                      </p>{" "}
                      <EditingIcon
                        className="w-4 h-4 ml-2 mt-1 text-gray-500 cursor-pointer"
                        onClick={handleEditName}
                      />
                    </div>
                  ) : (
                    <div>
                      <input
                        type="text"
                        value={
                          dashboardName
                          // ? dashboardName
                          // : dashboardList?.data?.name
                        }
                        onChange={(e) => handleDashboardNameError(e)}
                      />
                      {dashboardNameErr ? (
                        <p className="text-errorColor text-xs my-2">
                          {dashboardNameErr}
                        </p>
                      ) : null}
                    </div>
                  )}
                </div>
              </div>
              {!isloading &&
              cardDroppedData.length < 1 &&
              previewResponses.length < 1 &&
              !loading ? (
                <div className="text-center w-full my-auto h-screen mt-48">
                  Please select atleast one card or panel to save
                </div>
              ) : null}

              <div className=" mt-5 -ml-3 w-[68vw] min-h-[77%] relative">
                <ResponsiveLayoutCard
                  cardDroppedData={cardDroppedData}
                  removeCard={removeCard}
                  getGridData={getGridData}
                  isCreate={true}
                />
                <ResponsiveLayout
                  cardDroppedData={cardDroppedData}
                  panelDroppedData={previewResponses}
                  getGridData={getGridData}
                  removeCard={removeCard}
                  removePanel={removePanel}
                  isEdit={true}
                  setPreviewLoading={setPreviewLoading}
                  previewLoading={previewLoading}
                />
              </div>

              <div className="flex gap-3 mb-3 justify-end w-full">
                <CancelButton
                  label={"Cancel"}
                  buttonClassName="w-[100px] mb-3 h-9 text-xs "
                  onClick={() => {
                    navigate("/app/dashboard/details");
                  }}
                />
                <Button
                  type="submit"
                  label={"Save"}
                  buttonClassName="w-[100px] h-9 text-xs mr-3 mb-3"
                  onClick={() => updateAPICall()}
                  loading={updateLoading}
                  disabled={
                    (cardDroppedData.length < 1 &&
                      previewResponses.length < 1) ||
                    dashboardNameErr ||
                    previewLoading
                  }
                />
              </div>
            </div>

            <div className="bg-white p-2 rounded ">
              <div className="border border-panelBorder p-2.5 rounded ">
                <div className="flex flex-col gap-2">
                  <BackButton
                    label={"+ Add New Card"}
                    buttonClassName="w-full md:w-[200px] text-xs mb-2 border-errorBorder text-errorColor"
                    onClick={() => {
                      if (!roles.isSuperAdminRole) {
                        if (permissions.create === 1) {
                          openCard();
                        } else {
                          setShowAlertConfirmation(true);
                          setMessage("Create permission not allowed");
                        }
                      } else {
                        openCard();
                      }
                    }}
                  />
                  <BackButton
                    label={"+ Add New Panel"}
                    buttonClassName="w-full md:w-[200px] text-xs mb-2 border-errorBorder text-errorColor"
                    onClick={() => {
                      if (!roles.isSuperAdminRole) {
                        if (panelPermissions.create === 1) {
                          setCurrentStep(0);
                          setStepCount(0);
                          setFormData([]);
                          navigate("/app/panelmanagement/add", {
                            state: { Dashboard: true, id: value },
                          });
                        } else {
                          setShowAlertConfirmation(true);
                          setMessage("Create permission not allowed");
                        }
                      } else {
                        setCurrentStep(0);
                        setStepCount(0);
                        setFormData([]);
                        navigate("/app/panelmanagement/add", {
                          state: { Dashboard: true, id: value },
                        });
                      }
                    }}
                    // onClick={() => {
                    //   navigate("/app/panelmanagement/add", { state: "Dashboard" });
                    // }}
                  />
                  <AvailableItemsAccordion
                    title="Available Cards"
                    count={totalCount?.card}
                  >
                    <CardPreviewData
                      setTotalCount={setTotalCount}
                      isCardAdded={isCardAdded}
                      handleCardAddedFalse={handleCardAddedFalse}
                    />
                  </AvailableItemsAccordion>
                  <AvailableItemsAccordion
                    title="Available Panels"
                    count={totalCount?.panel}
                  >
                    <PanelPreviewData
                      setTotalCount={setTotalCount}
                      loadingData={loading}
                      previewLoading={previewLoading}
                    />
                  </AvailableItemsAccordion>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
      <AddCard
        openCard={openDialog}
        closeCard={closeCard}
        isAdd={true}
        onCardAdded={handleCardAdded}
      />
      <RemoveCard
        open={removeDialog}
        onCancelClick={() => setRemoveDialog(false)}
        message={message}
        message1={message1}
      />
      <SuccessDialog
        show={successDialog}
        onHide={() => {
          setSuccessDialog(false);
          navigate("/app/dashboard/details");
        }}
        message={message}
      />
      <ErrorDialog
        show={errorDialog}
        onHide={() => setErrorDialog(false)}
        message={message}
      />
      <InfoModal
        show={showAlertConfirmation}
        onHide={() => {
          setShowAlertConfirmation(false);
        }}
        message={message}
      />
    </>
  );
}

export default DashboradEdit;
