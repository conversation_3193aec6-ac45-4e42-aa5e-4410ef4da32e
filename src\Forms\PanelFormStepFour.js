import React, { useContext, useMemo, useState } from "react";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import { PreviewContext } from "../context/PreviewContext";
import Table from "../components/table/ReportTable";
import BackButton from "../components/Button/Button";
import Button from "../components/Button/OutlinedButton";
import dayjs from "dayjs";
import { createPanel, updatePanel } from "../services/panels-api";
import { useMutation } from "react-query";
import SuccessDialog from "../popups/SuccessDialog";
import { useNavigate } from "react-router-dom";
import DoughnutChart from "../components/charts/DoughnutChart";
import MultiAxisChart from "../components/charts/MultiAxisChart";
import LineChartPreview from "../components/reports/LineChartPreview";
import "react-toastify/dist/ReactToastify.css";
import { toast, ToastContainer } from "react-toastify";
import { InfoIcon } from "../icons";
import { CssTooltip } from "../components/StyledComponent";
import { handleDateFormat } from "../common/commonFunctions";
import { SelectedFiltersDisplay } from "../common/selectedFiltersDisplay";
import BarChartComponent from "../components/charts/BarCharts";

function PanelFormStepFour({ isEdit, value }) {
  const [suceessDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const navigate = useNavigate();

  const {
    formData,
    handleNextClick,
    setCurrentStep,
    currentStep,
    handlePrevClick,
    handleNextClickStep,
    handlePrevStep,
  } = useContext(multiStepFormContext);
  const { panelFormStepThreeResponse } = useContext(PreviewContext);
  const { mutate: creaatePanelAPI, isLoading: creationLoading } =
    useMutation(createPanel);
  const { mutate: updatePanelAPI, isLoading: upadteLoading } =
    useMutation(updatePanel);
  //console.log("newFormData", formData);

  const createAPICall = () => {
    let reqData = {
      name: formData.panelname,
      visualizationType: formData.type,
      dynamicReport: formData.dynamicReport,
      filters: [],
      dataColumns: {
        derivedFields: formData.derivedField,
      },
      interval: formData.interval,
    };

    if (formData.type === "Bar Graph") {
      reqData.dataColumns["X-Axis"] = formData.field;
      reqData.dataColumns.noOfRecords = parseInt(formData.records);
    }
    if (formData.type === "Table Report") {
      reqData.dataColumns.tableFields = formData.field.filter(
        (option) => option !== "Select All"
      );
    }

    // if (formData.range === "Calendar") {
    //   reqData.timePeriod = `${convertToUTC(
    //     formData.period.startDate
    //   )} to ${convertToUTC(formData.period.endDate)}`;
    // } else {
    //   reqData.timePeriod = formData.range;
    // }
    if (formData.range === "Calendar") {
      reqData.timePeriod = `${formData.period.startDate} to ${formData.period.endDate}`;
    } else {
      reqData.timePeriod = formData.range;
    }
    formData.conditions.forEach((condition) => {
      reqData.filters.push({
        field: condition.type1,
        condition: condition.type2,
        value: condition.type3,
        operator: condition.type4,
      });
    });

    if (isEdit) {
      updatePanelAPI(
        {
          value,
          reqData,
        },
        {
          onSuccess: (resp) => {
            setSuccessDialog(true);
            setMessage("Panel updated successfully");
            handleNextClickStep();
            //setCurrentStep(currentStep + 1);
            //console.log("currentStep", currentStep);
            // console.log("update response", resp);
          },
          onError: (error) => {
            toast.error(
              error.response.data.message ? error.response.data.message : error
            );
          },
        }
      );
    } else {
      creaatePanelAPI(
        {
          reqData,
        },
        {
          onSuccess: (resp) => {
            setSuccessDialog(true);
            setMessage("Panel created successfully");
            handleNextClickStep();
            // setCurrentStep(currentStep + 1);
            // console.log("currentStep", currentStep);
            console.log("save response", resp);
          },
          onError: (error) => {
            toast.error(
              error.response.data.message ? error.response.data.message : error
            );
          },
        }
      );
    }
  };

  const data = panelFormStepThreeResponse?.data?.data;
  const isDataEmpty = Object.keys(data)?.length === 0;
  //console.log("data", data);
  // Define columns for the table
  const columns = useMemo(() => {
    if (formData?.type === "Table Report") {
      if (!data || data.length === 0) {
        return [];
      }

      const firstItem = data[0];
      if (!firstItem) {
        return [];
      }

      const keys = Object.keys(firstItem);

      const dynamicColumns = keys.map((key) => ({
        header: key,
        accessorKey: key,
        Cell: ({ row }) => {
          const value = row.original[key];
          if (key === "Date") {
            return dayjs(value, { format: "YYYY-MM-DDTHH:mm:ss" }).isValid()
              ? dayjs(value).format("DD/MM/YYYY HH:mm:ss")
              : value;
          }
          return value;
        },
      }));

      return dynamicColumns;
    }
  }, [data]);

  const [startString, endString] = formData.range.split(" to ");
  let startDate = startString;
  let endDate = endString;
  const data1Formatted = handleDateFormat(formData.period.startDate);
  const data2Formatted = handleDateFormat(formData.period.endDate);
  return (
    <div>
      <div className="font-semibold flex-grow flex items-center justify-between mb-3  mx-16">
        <div className="text-sm">
          {"From: " + formData.period.startDate || startDate || ""}
          {" - "}
          {"To: " + formData?.period?.endDate || endDate || ""}
        </div>
        <div className=" flex  font-hebrew text-sm  whitespace-nowrap">
          {" "}
          <div className="flex">
            {" "}
            Selected Filters:
            <CssTooltip
              title={
                <SelectedFiltersDisplay conditions={formData.conditions} />
              }
              placement="bottom"
              arrow
            >
              <InfoIcon className="ml-2 mr-4 w-4 mt-1 h-3.5" />
            </CssTooltip>
          </div>
          <span className="text-errorColor"> Preview</span>
        </div>
      </div>
      <div className="md:flex gap-5 border border-outerBorder mx-16 p-3 ">
        {formData?.type === "Table Report" ? (
          <div
            style={{
              //  display: "flex",
              justifyContent: "center",
              width: "100%",
              padding: "24px",
              margin: "auto",
            }}
          >
            {isDataEmpty ? (
              <div className="d-flex font-semibold justify-center">
                No records to display..
              </div>
            ) : (
              <Table data={data || []} columns={columns} />
            )}
          </div>
        ) : formData.type === "Pie Chart" ? (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              width: "100%",
              height: "600px",
              margin: "auto",
            }}
          >
            {isDataEmpty ? (
              <div className="d-flex font-semibold justify-center">
                No records to display..
              </div>
            ) : (
              <DoughnutChart
                key={"panel"}
                radius={200}
                cutout={130}
                respData={data || []}
                panelData={true}
              />
            )}
          </div>
        ) : formData.type === "MultiAxis Graph" ? (
          <MultiAxisChart data={panelFormStepThreeResponse?.data} />
        ) : formData.type === "Bar Graph" ? (
          <BarChartComponent
            chartData={panelFormStepThreeResponse?.data?.data}
          />
        ) : formData.type === "Line Graph" ? (
          <LineChartPreview />
        ) : null}
      </div>
      <div className="flex-grow flex justify-end items-center mx-20 mt-20 mb-20 gap-4">
        <BackButton
          label={"Back"}
          buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded"
          onClick={() => {
            handlePrevClick();
            handlePrevStep();
          }}
        ></BackButton>
        <Button
          block="true"
          type="submit"
          label="Save"
          value="submit"
          buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded "
          onClick={() => createAPICall()}
          loading={creationLoading || upadteLoading}
        ></Button>
      </div>
      <SuccessDialog
        show={suceessDialog}
        onHide={() => {
          setSuccessDialog(false);
          if (formData?.dashboardValue) {
            if (formData?.id) {
              navigate(`/app/dashboard/details/edit/${formData.id}`);
            } else {
              navigate("/app/dashboard/details/create", {
                state: { name: formData.name },
              });
            }
          } else {
            navigate("/app/panelmanagement");
          }
        }}
        message={message}
      />
      <ToastContainer position="top-center" autoClose={3000} />
    </div>
  );
}

export default PanelFormStepFour;
