import React, { useState, useContext, useEffect } from "react";
import { useLocation } from "react-router";
import BackButton from "../../components/Button/Button";
import { useNavigate } from "react-router-dom";
import PanelPreviewData from "./PanelPreviewData";
import AddCard from "../../popups/AddCard";
import CardPreviewData from "./CardPreviewData";
import RemoveCard from "../../popups/RemoveCard";
import Button from "../../components/Button/OutlinedButton";
import CancelButton from "../../components/Button/Button";
import { createDashboard } from "../../services/dashboard-api";
import { useMutation } from "react-query";
import SuccessDialog from "../../popups/SuccessDialog";
import ResponsiveLayout from "./ResponsiveLayout";
import AvailableItemsAccordion from "../../components/Accordion/AvailableItemsAccordion";
import ErrorDialog from "../../popups/ErrorDialog";
import { multiStepFormContext } from "../../context/MultiStepFormContext";
import { AuthContext } from "../../context/AuthContext";
import InfoModal from "../../components/modals/InfoModal";
import BreadcrumbNavigation from "../../components/BreadCrumps/BreadCrump";
import { EditingIcon } from "../../icons";
import { useQuery } from "react-query";
import { getId, previewPanel } from "../../services/panels-api";
import dayjs from "dayjs";
import { convertToUTC } from "../../common/commonFunctions";
import ResponsiveLayoutCard from "./ResponsiveLayoutCard";

function DashboardCreation() {
  const [panelDroppedData, setPanelDroppedData] = useState([]);
  const [cardDroppedData, setCardDroppedData] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [removeDialog, setRemoveDialog] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState(false);
  const [message1, setMessage1] = useState(false);
  const [isCardAdded, setIsCardAdded] = useState(false);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  // const dashboardName = location?.state?.name;
  const [dashboardName, setDashboardName] = useState(location?.state?.name);
  const [editDashboardName, setEditDashboardName] = useState(false);
  const [dashboardNameErr, setDashboardNameErr] = useState("");
  const [panelId, setPanelId] = useState("");
  const [panelById, setPanelById] = useState("");
  const [responseData, setResponseData] = useState("");
  const [totalCount, setTotalCount] = useState({
    panel: 0,
    card: 0,
  });

  const { roles } = useContext(AuthContext);
  const { configApiData } = useContext(AuthContext);
  const { mutate: previewPanelAPIData, isLoading: loadingData } =
    useMutation(previewPanel);
  //console.log("configApiData", configApiData);
  let dimension = { w: 6, h: 3 };
  if (panelDroppedData.length > 0) {
    const lastEntry = panelDroppedData[panelDroppedData.length - 1];
    if (lastEntry.id === "dummy") {
      dimension.w = panelDroppedData.length > 1 ? 3 : 6;
    } else {
      dimension.w = 3;
    }
  }

  const permissions = roles.resources.filter(
    (res) => res.name === "Card Management"
  )[0].permissions;

  const panelPermissions = roles.resources.filter(
    (res) => res.name === "Panel Management"
  )[0].permissions;

  const { setCurrentStep, setStepCount, setFormData } =
    useContext(multiStepFormContext);

  const handleCardAdded = () => {
    setIsCardAdded(true);
  };

  const handleCardAddedFalse = () => {
    setIsCardAdded(false);
  };

  const { mutate: createDashboardAPI, isLoading: creationLoading } =
    useMutation(createDashboard);

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  useQuery(["getPanelById", panelId], getId, {
    enabled: !!panelId,
    onSuccess: ({ data }) => {
      //console.log("getPanelById", data);
      setPanelById(data);
      previewById(data);
    },
    refetchOnWindowFocus: false,
  });
  const previewById = (data) => {
    const selectedRange = data.timePeriod;

    let formattedStart = "";
    let formattedEnd = "";
    const currentDateTime = dayjs();

    if (selectedRange) {
      if (selectedRange.includes("to")) {
        const [startString, endString] = selectedRange.split("to");
        formattedStart = startString;
        formattedEnd = endString;
      } else {
        if (
          selectedRange === "Last Hour" ||
          selectedRange === "Last 6 Hours" ||
          selectedRange === "Last 12 Hours" ||
          selectedRange === "Last 24 Hours"
        ) {
          const hours = {
            "Last Hour": 1,
            "Last 6 Hours": 6,
            "Last 12 Hours": 12,
            "Last 24 Hours": 24,
          };

          const lastXHours = currentDateTime.subtract(
            hours[selectedRange],
            "hour"
          );
          if (selectedRange === "Last Hour") {
            formattedStart = lastXHours.format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
          } else {
            formattedStart = lastXHours.format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
          }
        } else if (selectedRange === "Today") {
          formattedStart = currentDateTime
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss");
          formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
        } else if (selectedRange === "Yesterday") {
          const yesterday = currentDateTime.subtract(1, "day");
          formattedStart = yesterday
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss");
          formattedEnd = yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss");
        } else if (selectedRange === "Last Seven Days") {
          formattedStart = currentDateTime
            .subtract(6, "days")
            .startOf("day")
            .format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
        } else if (selectedRange === "Last Week") {
          formattedStart = currentDateTime
            .subtract(1, "week")
            .startOf("week")
            .format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime
            .subtract(1, "week")
            .endOf("week")
            .format("YYYY-MM-DD HH:00:00");
        } else if (selectedRange === "Last 30 Days") {
          formattedStart = currentDateTime
            .subtract(29, "days")
            .startOf("day")
            .format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime
            .endOf("day")
            .format("YYYY-MM-DD HH:00:00");
        } else if (selectedRange === "Last Month") {
          formattedStart = currentDateTime
            .subtract(1, "month")
            .startOf("month")
            .format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime
            .subtract(1, "month")
            .endOf("month")
            .format("YYYY-MM-DD HH:00:00");
        } else if (selectedRange === "This Month") {
          formattedStart = currentDateTime
            .startOf("month")
            .format("YYYY-MM-DD HH:mm:ss");
          formattedEnd = currentDateTime
            .endOf("day")
            .format("YYYY-MM-DD HH:mm:ss");
        }
      }
    }

    let reqData = {
      name: data.name,
      visualizationType: data.visualizationType,
      filters: [],
      dataColumns: {
        derivedFields: data.dataColumns ? data.dataColumns.derivedFields : [],
      },
      //startDate: convertToUTC(formattedStart),
      //endDate: convertToUTC(formattedEnd),
      startDate: formattedStart,
      endDate: formattedEnd,
    };
    if (data.visualizationType === "Bar Graph") {
      reqData.dataColumns["X-Axis"] = data.dataColumns["X-Axis"];
      reqData.dataColumns.noOfRecords = parseInt(data.dataColumns.noOfRecords);
    } else {
      reqData.dataColumns.tableFields = data?.dataColumns?.tableFields;
    }
    if (
      data.visualizationType === "Line Graph" ||
      data.visualizationType === "MultiAxis Graph"
    ) {
      reqData.interval = data.interval;
    }
    data.filters.forEach((condition) => {
      reqData.filters.push({
        field: condition.field,
        condition: condition.condition,
        value: condition.value,
        operator: condition.operator,
      });
    });
    previewPanelAPIData(
      {
        reqData,
      },

      {
        refetchOnWindowFocus: false,
        onSuccess: (data) => {
          setResponseData({
            data: data?.data,
            formattedStart,
            formattedEnd,
            count: data?.count,
          });
        },

        onError: (error) => {
          setResponseData({
            data: "failed",
            formattedStart,
            formattedEnd,
          });
        },
      }
    );
  };

  useEffect(() => {
    if (responseData) {
      const panelData = {
        ...panelById,
        data:
          responseData?.data === "failed"
            ? "failed"
            : panelById.visualizationType === "MultiAxis Graph"
            ? responseData?.data
            : responseData?.data.data,
      };

      setPanelDroppedData((prevData) => {
        const filteredData = prevData.filter((item) => item.id !== "dummy");
        return [
          ...filteredData,
          {
            panelData: panelData,
            dimension,
            startDate: responseData?.formattedStart,
            endDate: responseData?.formattedEnd,
            count: responseData?.data?.count,
          },
        ];
      });
    }
  }, [responseData]);

  // console.log("panelById", panelDroppedData);
  useEffect(() => {
    if (loadingData) {
      //console.log("abc");
      const dimension = { w: panelDroppedData.length > 0 ? 3 : 6, h: 3 };
      const order = panelDroppedData.length + 1;
      setPanelDroppedData((prevResponses) => {
        const dummyEntry = {
          data: {},
          panelData: {},
          dimension: dimension,
          order: order,
          id: "dummy",
          timePeriod: "",
          count: 0,
        };
        return [...prevResponses, dummyEntry];
      });
    }
  }, [loadingData]);

  const handleDrop = (e) => {
    try {
      const droppedData = JSON.parse(e.dataTransfer.getData("data"));
      //console.log("droppedData123", droppedData);
      const addPanel = () => {
        const panelIdData = droppedData.data;

        const maxAllowedPanels = roles?.isSuperAdminRole
          ? configApiData?.MAX_PANELS_PER_DASHBOARD
          : roles?.panelCount;

        const isPanelAlreadyDropped = panelDroppedData.some(
          (panel) => panel.panelData.id === panelIdData
        );

        if (!isPanelAlreadyDropped) {
          if (panelDroppedData.length < maxAllowedPanels) {
            setPanelId(droppedData.data);
          } else {
            setShowAlertConfirmation(true);
            setMessage(
              `Maximum ${maxAllowedPanels} panels allowed in a dashboard`
            );
          }
        } else {
          setShowAlertConfirmation(true);
          setMessage("Already the panel is added");
        }
      };

      const addCard = (cardData) => {
        const maxAllowedCards = roles?.isSuperAdminRole
          ? configApiData?.MAX_CARDS_PER_DASHBOARD
          : 4;
        const isCardAlreadyDropped = cardDroppedData.some(
          (card) => card.id === cardData.id
        );
        if (!isCardAlreadyDropped) {
          if (cardDroppedData.length < maxAllowedCards) {
            setCardDroppedData((prevData) => [...prevData, cardData]);
          } else {
            setShowAlertConfirmation(true);
            setMessage(
              `Maximum ${maxAllowedCards} cards allowed in a dashboard`
            );
          }
        } else {
          setShowAlertConfirmation(true);
          setMessage("Already the card is added");
        }
      };

      if (droppedData.type === "panel") {
        // const { panel, previewResponse } = droppedData?.data;
        // const panelData = {
        //   ...panel,

        //   data:
        //     panel.visualizationType === "MultiAxis Graph"
        //       ? previewResponse?.data
        //       : previewResponse?.data.data,
        // };

        addPanel();
      } else if (droppedData.type === "card") {
        const cardData = droppedData?.data;
        addCard(cardData);
      }
    } catch (error) {
      e.preventDefault();
    }
  };

  function openCard() {
    setOpenDialog(true);
  }

  function closeCard() {
    setOpenDialog(false);
  }

  const removeCard = (index) => {
    setCardDroppedData((prevData) => prevData.filter((_, i) => i !== index));
  };
  const removePanel = (index) => {
    setPanelDroppedData((prevData) => {
      const newData = prevData.filter((_, i) => i !== index);
      return newData.map((ele, i) => {
        let dimension = { w: i > 0 ? 3 : 6, h: 3 };
        return { ...ele, dimension };
      });
    });
    setPanelId("");
  };

  const createAPICall = () => {
    const cardIds = cardDroppedData.map((card) => card.id);
    const panelIds = panelDroppedData.map((panel) => panel.panelData.id);

    let reqData = {
      name: dashboardName,
      cards: cardIds.map((id, index) => ({ id, order: index + 1 })),
      panels: panelIds.map((id, index) => ({ id, order: index + 1 })),
    };

    createDashboardAPI(
      {
        reqData,
      },
      {
        onSuccess: (resp) => {
          setSuccessDialog(true);
          setMessage("Dashboard created successfully");
        },
        onError: ({ response }) => {
          //console.log("save response", response);
          setErrorDialog(true);
          setMessage(response?.data?.message);
        },
      }
    );
  };
  const getGridData = (index, dimension) => {
    let dim = { x: 0, y: 0, w: dimension.w, h: dimension.h };

    switch (index) {
      case 0:
        return dim;
      default:
        dim.x = index % 2 === 0 ? 6 : 0;
        dim.y = index % 2 === 0 ? 6 : 3;
        return dim;
    }
  };

  const handleDashboardNameError = (e) => {
    setDashboardName(e.target.value);
    if (
      !/^(?=.*[a-zA-Z])[a-zA-Z0-9_\s!@#$%^&*()\-+=~`'<>.,/?;:{}[\]|\\]*$/.test(
        e.target.value
      )
    ) {
      setDashboardNameErr("Enter atleast one alphabet");
    } else if (e.target.value?.length < 2) {
      setDashboardNameErr("Min length allowed is 2 characters");
    } else if (e.target.value?.length > 256) {
      setDashboardNameErr("Max length allowed is 256");
    } else {
      setDashboardNameErr("");
    }
  };

  return (
    <>
      <div
        className="flex mt-5 h-[95%] w-full"
        onDragOver={handleDragOver}
        onDrop={(e) => handleDrop(e)}
      >
        <div>
          <div className="cursor-pointer ">
            <BreadcrumbNavigation
              linkTwo={"Dashboard"}
              onlinkTwoClick={() => navigate("/app/dashboard/details")}
              title={dashboardName}
            />
            <div className="mt-6 ">
              {!editDashboardName ? (
                <div className=" flex">
                  <p>{dashboardName}</p>{" "}
                  <EditingIcon
                    className="w-4 h-4 ml-2 mt-1 text-gray-500 cursor-pointer"
                    onClick={() => setEditDashboardName(true)}
                  />
                </div>
              ) : (
                <div>
                  <input
                    type="text"
                    value={dashboardName}
                    onChange={(e) => handleDashboardNameError(e)}
                  />
                  {dashboardNameErr ? (
                    <p className="text-errorColor text-xs my-2">
                      {dashboardNameErr}
                    </p>
                  ) : null}
                </div>
              )}
            </div>
          </div>
          <div>
            {cardDroppedData.length < 1 &&
            panelDroppedData.length < 1 &&
            !loadingData ? (
              <div className="text-center mt-32">
                Please create or drag and drop the cards or panels
              </div>
            ) : null}
          </div>{" "}
          <div className="text-sm font-normal mt-2 -ml-3 w-[68vw]  min-h-[46%] relative">
            <ResponsiveLayoutCard
              cardDroppedData={cardDroppedData}
              removeCard={removeCard}
              getGridData={getGridData}
              isCreate={true}
            />

            <ResponsiveLayout
              cardDroppedData={cardDroppedData}
              removeCard={removeCard}
              panelDroppedData={panelDroppedData}
              removePanel={removePanel}
              getGridData={getGridData}
              isCreate={true}
              setPreviewLoading={setPreviewLoading}
              previewLoading={previewLoading}
            />
          </div>
          <div className="flex gap-3 mb-3 justify-end w-full">
            <CancelButton
              label={"Cancel"}
              buttonClassName="w-[100px] h-9 text-xs mb-3"
              onClick={() => {
                navigate("/app/dashboard/details");
              }}
            />
            <Button
              type="submit"
              label={"Save"}
              buttonClassName="w-[100px] h-9 text-xs  mr-3 "
              onClick={() => createAPICall()}
              loading={creationLoading}
              disabled={
                (cardDroppedData.length === 0 &&
                  panelDroppedData.length === 0) ||
                dashboardNameErr ||
                loadingData
              }
            />
          </div>
        </div>

        <div className="bg-white p-2 rounded mr-20">
          <div className="border border-panelBorder bg-white p-2.5 rounded ">
            <div className="flex flex-col gap-2">
              <BackButton
                label={"+ Add New Card"}
                buttonClassName="w-full md:w-[200px] text-xs mb-2 border-errorBorder text-errorColor"
                onClick={() => {
                  if (!roles.isSuperAdminRole) {
                    if (permissions.create === 1) {
                      openCard();
                    } else {
                      setShowAlertConfirmation(true);
                      setMessage("Create permission not allowed");
                    }
                  } else {
                    openCard();
                  }
                }}
              />
              <BackButton
                label={"+ Add New Panel"}
                buttonClassName="w-full md:w-[200px] text-xs mb-2 border-errorBorder text-errorColor"
                onClick={() => {
                  if (!roles.isSuperAdminRole) {
                    if (panelPermissions.create === 1) {
                      setCurrentStep(0);
                      setStepCount(0);
                      setFormData([]);
                      navigate("/app/panelmanagement/add", {
                        state: { Dashboard: true, name: dashboardName },
                      });
                    } else {
                      setShowAlertConfirmation(true);
                      setMessage("Create permission not allowed");
                    }
                  } else {
                    setCurrentStep(0);
                    setStepCount(0);
                    setFormData([]);
                    navigate("/app/panelmanagement/add", {
                      state: { Dashboard: true, name: dashboardName },
                    });
                  }
                }}
              />
              <AvailableItemsAccordion
                title="Available Cards"
                count={totalCount?.card}
              >
                <CardPreviewData
                  isCardAdded={isCardAdded}
                  setTotalCount={setTotalCount}
                  handleCardAddedFalse={handleCardAddedFalse}
                />
              </AvailableItemsAccordion>
              <AvailableItemsAccordion
                title="Available Panels"
                count={totalCount?.panel}
              >
                <PanelPreviewData
                  setTotalCount={setTotalCount}
                  loadingData={loadingData}
                />
              </AvailableItemsAccordion>
            </div>
          </div>
        </div>
      </div>

      <AddCard
        openCard={openDialog}
        closeCard={closeCard}
        isAdd={true}
        onCardAdded={handleCardAdded}
      />
      <RemoveCard
        open={removeDialog}
        onCancelClick={() => setRemoveDialog(false)}
        message={message}
        message1={message1}
      />
      <SuccessDialog
        show={successDialog}
        onHide={() => {
          setSuccessDialog(false);
          navigate("/app/dashboard/details");
        }}
        message={message}
      />
      <ErrorDialog
        show={errorDialog}
        onHide={() => {
          setErrorDialog(false);
          navigate("/app/dashboard/details");
        }}
        message={message}
      />
      <InfoModal
        show={showAlertConfirmation}
        onHide={() => {
          setShowAlertConfirmation(false);
        }}
        message={message}
      />
    </>
  );
}

export default DashboardCreation;
