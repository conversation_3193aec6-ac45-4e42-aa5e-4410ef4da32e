import { CssTextField } from "../../StyledComponent";
import { useField } from "formik";
import InputLabel from "../InputLabel/InputLabel";

const TextFieldWrapper = ({
  name,
  label,
  labelColor,
  labelClassName,
  layoutClassName,
  isMandatory,
  ...rest
}) => {
  const [field, meta] = useField(name);

  const handleKeyDown = (event) => {
    if (event.key === "Backspace") {
      event.stopPropagation();
    }
  };

  const configTextField = {
    ...field,
    ...rest,
    fullWidth: true,
    autoComplete: "off",
    onKeyDown: handleKeyDown,
  };

  if (meta && meta.touched && meta.error) {
    configTextField.error = true;
    // configTextField.helperText = meta.error;
  }

  return (
    <div className={layoutClassName}>
      {label && (
        <InputLabel
          label={label}
          color={labelColor}
          labelClassName={labelClassName}
          isMandatory={isMandatory}
        />
      )}
      <CssTextField
        {...configTextField}
        inputProps={{
          autoComplete: "off",
        }}
      />
      {meta.error && (
        <div className=" text-[#d32f2f] text-[0.75rem] mt-0 font-normal">
          {meta.error}
        </div>
      )}
      {/* <CssTextField
        {...configTextField}
        inputProps={{
          autoComplete: "off",
        }}
        // FormHelperTextProps={{
        //   margin: "0",
        // }}
      /> */}
    </div>
  );
};

export default TextFieldWrapper;
