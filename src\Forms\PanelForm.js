import React, { useContext, useState } from "react";
import { Form, Formik, Field } from "formik";
import TextFieldWrapper from "../components/FormsUI/TextField";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import Select from "../components/FormsUI/Select";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import Button from "../components/Button/OutlinedButton";
import { getVisualizationType } from "../services/dropdown-api";
import { useQuery } from "react-query";
import * as Yup from "yup";
import { useLocation } from "react-router-dom";

function PanelForm({ editDetail }) {
  const [visualType, setVisualType] = useState([]);
  const { handleNextClick, setFormData, formData, handleNextClickStep } =
    useContext(multiStepFormContext);
  const location = useLocation();
  const dashboardData = location?.state?.Dashboard;
  const name = location?.state?.name;
  const id = location?.state?.id;

  useQuery(["/visualizationList"], getVisualizationType, {
    onSuccess: (res) => {
      const visualTypeList = res.data.map((type) => ({
        value: type,
        label: type,
      }));
      setVisualType(visualTypeList);
    },
  });

  const onlySpaceRegex = /(?=.*[a-z])|(?=.*[A-Z])/;
  const validationSchema = Yup.object().shape({
    panelname: Yup.string()
      .required("Panel Name is Required")
      .max(256, "Max length allowed is 256 characters")
      .min(1, "Min length allowed is 1 character")
      .matches(onlySpaceRegex, "Must contain at least one alphabet"),
    type: Yup.string().required("Type of Visualization is Required"),
    dynamicReport: Yup.number()
      .oneOf([0, 1], "Please select Yes or No")
      .required("Please select Yes or No"),
  });

  const handleSelectedOption = (options) => {
    if (formData?.type !== options) {
      setFormData({
        ...formData,
        derivedField: [],
        conditions: [{ type1: "", type2: "", type3: "", type4: "" }],
        field: [],
        range: "",
        startDate: "",
        endDate: "",
        interval: "",
      });
    }
  };

  return (
    <div>
      <Formik
        initialValues={{
          panelname: formData?.panelname || editDetail?.name || "",
          type: formData?.type || editDetail?.visualizationType || "",
          dynamicReport:
            formData?.dynamicReport ??
            (editDetail?.dynamicReport !== undefined
              ? editDetail.dynamicReport
              : null),
        }}
        validateOnMount={true}
        validationSchema={validationSchema}
        enableReinitialize={true}
        onSubmit={(values) => {
          handleNextClick();
          handleNextClickStep();
          const newFormData = Object.assign(formData, values, {
            dashboardValue: dashboardData,
            name: name,
            id: id,
          });
          setFormData(newFormData);
        }}
      >
        {({ values, errors, touched, setFieldValue }) => (
          <Form>
            <div className="flex flex-col items-center justify-center mb-4 mx-10 md:mx-0">
              <div className="w-full md:w-[500px]">
                <InputLabel label={"Panel Name"} isMandatory={true} />
                <TextFieldWrapper
                  name="panelname"
                  placeholder={"Enter panel name"}
                />
              </div>

              <div className="w-full md:w-[500px] mt-7">
                <InputLabel
                  label={"Type of Visualization"}
                  isMandatory={true}
                />
                <Select
                  name="type"
                  options={visualType}
                  placeholder={"Select visualization type"}
                  setFormData={setFormData}
                  type={"visualType"}
                  visTypeDropdown={true}
                  onChange={(selectedOption) => {
                    handleSelectedOption(selectedOption.label);
                  }}
                  isSearchable={true}
                />
              </div>

              <div className="w-full md:w-[500px] mt-7">
                <InputLabel label="Dynamic Reports" isMandatory={true} />
                <div className="flex gap-10 mt-2">
                  <label className="flex items-center gap-2">
                    <Field
                      type="radio"
                      name="dynamicReport"
                      value={1}
                      as="input"
                      checked={values.dynamicReport === 1}
                      onChange={() => setFieldValue("dynamicReport", 1)}
                      className="accent-black"
                    />
                    <p className="text-xs">Yes</p>
                  </label>
                  <label className="flex items-center gap-2">
                    <Field
                      type="radio"
                      name="dynamicReport"
                      value={0}
                      as="input"
                      checked={values.dynamicReport === 0}
                      onChange={() => setFieldValue("dynamicReport", 0)}
                      className="accent-black"
                    />
                    <p className="text-xs">No</p>
                  </label>
                </div>
                {errors.dynamicReport && touched.dynamicReport && (
                  <div className="text-errorColor text-xs mt-1">
                    {errors.dynamicReport}
                  </div>
                )}
              </div>
            </div>

            <div className="flex-grow flex justify-end items-center mx-20 mt-10">
              <Button
                block="true"
                type="submit"
                label="Next"
                value="submit"
                buttonClassName="w-full md:w-[154px] h-[40px] text-xs mb-3 rounded "
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}

export default PanelForm;
