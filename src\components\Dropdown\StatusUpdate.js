// import { Formik, Form } from "formik";
// import React from "react";
// import Select from "../FormsUI/RadioSelect";

// function StatusUpdate({ statusOptions, value }) {
//   const handleUpdate=()=>{

//   }
//   return (
//     <div>
//       <Formik
//         initialValues={{
//           statusDetails: value || "",
//         }}
//       >
//         {({ values, errors, status }) => (
//           <Form>
//             <div className="flex items-center">
//               <div className="w-[150px]">
//                 <Select
//                   name="statusDetails"
//                   options={statusOptions || []}
//                   handleUpdate={handleUpdate}
//                   onChange={(selectedOption) => {
//                     console.log("selectedOption", selectedOption);
//                     // setGlobalSearchSelect(selectedOption.value);
//                   }}
//                 />
//               </div>
//             </div>
//           </Form>
//         )}
//       </Formik>
//     </div>
//   );
// }

// export default StatusUpdate;
import { Formik, Form } from "formik";
import React from "react";
import Select from "../FormsUI/RadioSelect";
import { useMutation } from "react-query";
import axios from "axios";
import { alertServices } from "../../services/alert-api";

function StatusUpdate({ statusOptions, value, id }) {
  const { mutate: handleStatusUpdateAPI, isLoading: isUpdateLoading } =
    useMutation(alertServices.handleStatusUpdate);

  const handleUpdate = (updatedStatus) => {
    handleStatusUpdateAPI({ status: updatedStatus, id });
  };

  return (
    <div>
      <Formik
        initialValues={{
          statusDetails: value || "",
        }}
      >
        {({ setFieldValue }) => (
          <Form>
            <div className="flex items-center">
              <div className="w-[150px]">
                <Select
                  name="statusDetails"
                  options={statusOptions || []}
                  handleUpdate={(value) => handleUpdate(value)}
                  onChange={(selectedOption) => {
                    setFieldValue("statusDetails", selectedOption?.value || "");
                  }}
                />
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}

export default StatusUpdate;
