import React, { useMemo, useState, useEffect, useContext, useRef } from "react";
import Pagination from "../components/Pagination/Pagination";
import { reportService } from "../services/staticreport.service";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import ExportPopup from "../popups/exportpopup";
import {
  MailBoxIcon,
  CloseIcon,
  SearchhIcon,
  InfoIcon,
  RefreshIcon,
  FilterIcon,
  MenusIcon,
  FileRefreshIcon,
} from "../icons";
import { AuthContext } from "../context/AuthContext";
import InfoModal from "../components/modals/InfoModal";
import { DataContext } from "../context/DataContext";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import { useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
import { formatDate } from "../utils/fileDateFormator";
import Button from "../components/Button/OutlinedButton";
import SendMail from "../popups/SendMail";
import { CssTooltip } from "../components/StyledComponent";
import bgImage from "../assets/img/Records.png";
import {
  defaultTimeRange,
  dynamicReports,
  timeAcessOptions,
} from "../common/constants";
import ErrorDialog from "../popups/ErrorDialog";
import BreadcrumbNavigation from "../components/BreadCrumps/BreadCrump";
import { DownloadContext } from "../context/DownloadContext";
import { formatDateTime } from "../common/commonFunctions";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import theme from "../tailwind-theme";
import ReportTable from "../components/table/ReportTable";
import AggregationTable from "../components/table/AggregationTable";
import ReportFilter from "../components/CollapsibleFilter/ReportFilter";
import { MetaDataProvider } from "../context/MetaDataContext";
import SuccessDialog from "../popups/SuccessDialog";
import ReportCalendar from "../components/DatePicker/ReportCalendar";
import { NO_FILTER } from "../utils/constants";
import { billingReportRefresh } from "../services/cdrsearch-api";
import { useMutation } from "react-query";

function StaticReports({ onClose }) {
  const location = useLocation();
  const { value: data, viewBy, activeTab, subtype } = location.state || {};
  const { resultPerPage } = useContext(DataContext);
  const [limitPerPage, setLimitPerPage] = useState(100);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState("");
  const [details, setDetails] = useState([]);
  const [filters, setFilters] = useState([]);
  const [aggrgationDetail, setAggrgationDetail] = useState([]);
  const [showExportConfirmation, setShowExportConfirmation] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [labelData, setLabelData] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState(() => {
    const isSupplierOrCustomer =
      data === dynamicReports.Supplier || data === dynamicReports.Customer;
    const isSlabBasedBilling =
      data === dynamicReports.SlabBasedCust ||
      data === dynamicReports.SlabBasedSupp;

    const startDate = isSlabBasedBilling
      ? dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss")
      : isSupplierOrCustomer
      ? dayjs().subtract(6, "days").startOf("day").format("YYYY-MM-DD HH:00:00")
      : dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss");

    const endDate = dayjs().format("YYYY-MM-DD HH:mm:ss");

    return { startDate, endDate };
  });
  const [selectedRange, setSelectedRange] = useState(() => {
    if (data === dynamicReports.Supplier || data === dynamicReports.Customer) {
      return "Last Seven Days";
    } else if (
      data === dynamicReports.SlabBasedCust ||
      data === dynamicReports.SlabBasedSupp
    ) {
      return "This Month";
    }
    return "Today";
  });
  const [searchStr, setSearchStr] = useState("");
  const [reload, setReload] = useState(false);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [sendMailDialog, setSendMailDialog] = useState(false);
  const [filterDialog, setFilterDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [extensionType, setExtensionType] = useState("");
  const [errorDialog, setErrorDialog] = useState(false);
  const [refreshClick, setRefreshClick] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [showMenuDropdown, setShowMenuDropdown] = useState(false);
  const [showFtpOptions, setShowFtpOptions] = useState(false);
  const [ftpRefreshType, setFtpRefreshType] = useState("");
  const [openDialog, setOpenDialog] = useState(false);

  const menuRef = useRef(null);

  const navigate = useNavigate();

  dayjs.extend(customParseFormat);

  const { isDownloading, setIsDownloading } = useContext(DownloadContext);
  const { roles, configApiData, user } = useContext(AuthContext);

  const matchingReports = roles?.staticReports?.filter((report) => {
    return report.name === data;
  });
  const permission = user.isSuperAdmin
    ? 1
    : matchingReports[0]?.permissions?.download;

  const isAdmin = user.isSuperAdmin;

  function handlePageChange(page) {
    setCurrentPage(page);
  }

  const getUrl = new URL(window.location.href);
  const timeZone = getUrl.search.slice(1);
  const isExportAllowed = (totalCount) => {
    const {
      MAX_ROWS_FOR_CSV,
      MAX_ROWS_FOR_PDF,
      MAX_ROWS_FOR_EXCEL,
      INITIATE_OFFLINE_DOWNLOAD,
    } = configApiData;

    const isOfflineDownload = totalCount >= INITIATE_OFFLINE_DOWNLOAD;

    return {
      csv: isOfflineDownload || totalCount <= MAX_ROWS_FOR_CSV,
      pdf: isOfflineDownload ? false : totalCount <= MAX_ROWS_FOR_PDF,
      excel: isOfflineDownload ? false : totalCount <= MAX_ROWS_FOR_EXCEL,
    };
  };

  const exportPermissions = isExportAllowed(totalCount);

  const handleLimitChange = (e) => {
    setCurrentPage(1);
    setLimitPerPage(e?.target?.value);
    if (e.target.value > 1000) {
      toast.warn("There might be delay in loading larger data.");
    }
  };

  const handleKeyUp = (event) => {
    const value = event.target.value;
    setSearchStr(value);
    setReload(true);

    if (value === "") {
      setCurrentPage(1);
      setSearchStr("");
      setReload(true);
    } else {
      setCurrentPage(1);
    }
  };
  useEffect(() => {
    if (data !== "") {
      setIsLoading(true);

      reportService
        .getReport(
          data,
          currentPage,
          limitPerPage,
          selectedFilter.startDate,
          selectedFilter.endDate,
          searchStr,
          timeZone,
          filters,
          selectedFilter.duration
        )
        .then((res) => {
          let newData = res?.data?.data?.map((x) => {
            const newObj = { ...x };
            // if (
            //   data !== dynamicReports.Supplier &&
            //   data !== dynamicReports.Customer &&
            //   data !== dynamicReports.Traffic &&
            //   data !== dynamicReports.Facebook &&
            //   data !== dynamicReports.SlabBasedCust &&
            //   data !== dynamicReports.SlabBasedSupp &&
            //   data !== dynamicReports.SupplierMultiple &&
            //   data !== dynamicReports.CustomerMutiple
            // ) {
            //   newObj.Date = formatDateTime(x.Date);
            // }
            return newObj;
          });
          setDetails(newData);
          setAggrgationDetail(res.data.aggregateResponse);
          setTotalCount(res.data.totalCount);
        })
        .catch((err) => {
          setDetails([]);
          setAggrgationDetail([]);
        })
        .finally(() => {
          setIsLoading(false);
          setReload(false);
          setRefreshClick(false);
        });
    }
  }, [
    currentPage,
    limitPerPage,
    selectedFilter,
    reload,
    refreshClick,
    filters,
  ]);

  useEffect(() => {
    setCurrentPage(1);
  }, [selectedFilter]);
  const exportReport = (type) => {
    let extensionType =
      type === "CSV" ? ".csv" : type === "EXCEL" ? ".xlsx" : ".pdf";
    const startDate = selectedFilter.startDate.split(" ")[0];
    const endDate = selectedFilter.endDate.split(" ")[0];
    const currentTime = formatDate();
    const fileName = `${
      selectedRange === "This Year" || selectedRange === "Last Year"
        ? `Yearly ${data} ${startDate} - ${endDate} ${currentTime}${
            filters && filters.length !== 0 ? "_filters" : ""
          }`
        : `${data} ${startDate} - ${endDate} ${currentTime}${
            filters && filters.length !== 0 ? "_filters" : ""
          }`
    }`;
    setExtensionType(type);
    if (totalCount < configApiData.INITIATE_OFFLINE_DOWNLOAD) {
      setIsDownloading(true);
    }
    if (type !== "") {
      reportService
        .downloadReport(
          data,
          "1",
          type,
          selectedFilter.startDate,
          selectedFilter.endDate,
          searchStr,
          timeZone,
          totalCount,
          filters,
          selectedFilter.duration,
          configApiData.INITIATE_OFFLINE_DOWNLOAD,
          fileName
        )
        .then((blob) => {
          if (totalCount > configApiData.INITIATE_OFFLINE_DOWNLOAD) {
            setSuccessDialog(true);
            setMessage(blob.data.message);
          } else {
            const url = URL.createObjectURL(blob.data);
            const link = document.createElement("a");
            const filename = fileName + `${extensionType}.zip`;
            link.href = url;
            link.download = filename;
            link.click();
          }
        })
        .catch((error) => {
          setErrorDialog(true);
          setMessage("No record found");
        })
        .finally(() => {
          setIsDownloading(false);
        });
    }
  };

  const columns = useMemo(() => {
    if (!details || details.length === 0) return [];

    const firstItem = details[0];
    const keys = Object.keys(firstItem);

    const dynamicColumns = keys.map((key) => ({
      id: key,
      accessorKey: key,
      header: key,
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => row.original[key],
    }));

    return dynamicColumns;
  }, [details]);

  const getTimeRange = (viewBy) => {
    if (viewBy.length === 5) {
      return defaultTimeRange;
    } else {
      let newRange = viewBy.map((x) => {
        return timeAcessOptions[x];
      });
      return [...new Set(newRange.flat(1))];
    }
  };

  const filteredKeys = Object.keys(filters).filter(
    (key) => key !== "durationTime"
  );
  const badgeCount = filteredKeys.length;

  const removeFilter = NO_FILTER.some((filter) => filter === data);

  const tooltipContent = (
    <div className="text-sm">
      <div className="font-bold text-xs mb-1">
        Configured number of rows for download:
      </div>
      <div className="font-semibold text-xs mb-1">
        {" "}
        CSV:{" "}
        {configApiData?.MAX_ROWS_FOR_CSV?.toLocaleString("en-IN") ||
          "Loading..."}
      </div>
      <div className="font-semibold text-xs mb-1">
        EXCEL:{" "}
        {configApiData?.MAX_ROWS_FOR_EXCEL?.toLocaleString("en-IN") ||
          "Loading..."}
      </div>
      <div className="font-semibold text-xs mb-1">
        PDF:{" "}
        {configApiData?.MAX_ROWS_FOR_PDF?.toLocaleString("en-IN") ||
          "Loading..."}
      </div>
      <div className="font-semibold text-xs mb-1">
        Offline (Only in CSV):{" "}
        {configApiData?.INITIATE_OFFLINE_DOWNLOAD?.toLocaleString("en-IN") ||
          "Loading..."}
      </div>
    </div>
  );

  const { mutate: ftpRefreshAPI } = useMutation(billingReportRefresh);

  const handleFtpRefresh = (type) => {
    let startDate, endDate;

    if (type === "This Month") {
      startDate = dayjs().startOf("month");
      endDate = dayjs();
    } else if (type === "Last Month") {
      const lastMonth = dayjs().subtract(1, "month");
      startDate = lastMonth.startOf("month");
      endDate = lastMonth.endOf("month");
    } else {
      return;
    }

    const reqObj = {
      reportName: data,
      timezone: timeZone,
      startDate: startDate.format("YYYY-MM-DD HH:mm:ss"),
      endDate: endDate.format("YYYY-MM-DD HH:mm:ss"),
      isRefresh: true,
    };
    ftpRefreshAPI(
      { reqObj },
      {
        onSuccess: ({ data }) => {
          setSuccessDialog(true);
          setMessage(data?.data?.message);
        },
        onError: () => {
          setErrorDialog(true);
          setMessage("Something went wrong ! Try Again");
        },
      }
    );

    setShowFtpOptions(false);
    setShowMenuDropdown(false);
  };

  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setShowMenuDropdown(false);
      }
    }

    if (showMenuDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showMenuDropdown]);

  return (
    <>
      {/* Sticky Header with Breadcrumb */}
      <div className="sticky top-0 z-10 w-full bg-bgPrimary flex items-start text-headingColor text-2xl font-bold leading-tight">
        <BreadcrumbNavigation
          linkTwo="Static Reports"
          onlinkTwoClick={() =>
            navigate("/app/reports", {
              state: { tab: activeTab, subType: subtype },
            })
          }
          title={data}
        />
      </div>

      {/* Main Content Container */}
      <div className="bg-white p-3">
        {/* Close Icon Section */}
        <div className="flex justify-end items-center">
          <CloseIcon
            onClick={() =>
              navigate("/app/reports", {
                state: { tab: activeTab, subType: subtype },
              })
            }
            className="w-2 h-2 cursor-pointer mb-1"
          />
        </div>

        {/* Search and Controls */}
        <div className="mx-3 flex flex-wrap items-center justify-between gap-y-3">
          {/* Search Input */}
          <div className="w-full md:w-[300px] relative">
            <input
              type="text"
              style={{
                border: `1px solid ${theme.borderColor.outerBorder}`,
                paddingLeft: "2.5rem",
              }}
              className="w-full text-tabColor bg-white rounded-md focus:outline-none text-sm h-10"
              placeholder="Search"
              value={searchStr}
              onChange={handleKeyUp}
            />
            <div className="absolute top-3 left-3">
              <SearchhIcon className="w-4 h-4" />
            </div>
          </div>

          {/* Calendar, Info Tooltip, Mail, Filter, Refresh, Download */}
          <div className="flex items-center space-x-8">
            {/* Menu Icon with Dropdown - Now positioned first */}
            <div className="relative" ref={menuRef}>
              <div
                className="rounded-full bg-bgouterBackground p-2 flex items-center justify-center cursor-pointer"
                onClick={() => {
                  setShowMenuDropdown(!showMenuDropdown);
                  setShowFtpOptions(false);
                  setOpenDialog(false);
                }}
              >
                <CssTooltip title={"Report Menu"} placement="top" arrow>
                  <MenusIcon className="w-5 h-5" />
                </CssTooltip>
              </div>

              {/* Dropdown Menu */}
              {showMenuDropdown && (
                <div className="absolute right-0 mt-2 w-72 bg-white rounded-md shadow-lg z-10 border border-gray-200 p-3">
                  <div className="grid grid-cols-2 gap-2">
                    {/* Send Mail Option */}
                    <div
                      className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
                      onClick={() => {
                        setSendMailDialog(true);
                        setShowMenuDropdown(false);
                        setShowFtpOptions(false);
                      }}
                    >
                      <MailBoxIcon className="w-5 h-5" />
                      <span className="text-sm text-gray-700 ml-2 ">
                        Send Email
                      </span>
                    </div>

                    {/* Filters Option */}
                    <div
                      className={`flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100 ${
                        removeFilter ? "opacity-50 pointer-events-none" : ""
                      }`}
                      onClick={() => {
                        if (!removeFilter) {
                          setFilterDialog(true);
                          setShowMenuDropdown(false);
                        }
                        setShowFtpOptions(false);
                      }}
                    >
                      <div className="relative">
                        <FilterIcon className="w-5 h-5" />
                        {badgeCount > 0 && (
                          <CssTooltip
                            title={
                              labelData.length > 0 ? labelData.join(", ") : ""
                            }
                            placement="top"
                            arrow
                          >
                            <div className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full h-4 w-4 flex items-center justify-center text-xs font-medium">
                              {badgeCount}
                            </div>
                          </CssTooltip>
                        )}
                      </div>
                      <span className="text-sm text-gray-700 ml-2 truncate">
                        Filters
                      </span>
                    </div>

                    {/* Refresh Option (Only for Billing Tab) */}
                    {activeTab === 2 && subtype === "Billing" && (
                      <div
                        className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
                        onClick={() => {
                          setRefreshClick(true);
                          setShowMenuDropdown(false);
                          setShowFtpOptions(false);
                          toast.success("Refresh Applied...");
                        }}
                      >
                        <RefreshIcon className="w-5 h-5" />
                        <span className="text-sm text-gray-700 ml-2 truncate">
                          Refresh
                        </span>
                      </div>
                    )}

                    {/* FTP Refresh Option with Submenu */}
                    {configApiData?.MONTHLY_BILLING_REPORTS.includes(data) &&
                      isAdmin && (
                        <>
                          {" "}
                          <div
                            className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
                            onClick={() => setShowFtpOptions(!showFtpOptions)}
                          >
                            <FileRefreshIcon className="w-5 h-5" />
                            <span className="text-sm text-gray-700 ml-2 truncate">
                              FTP Refresh
                            </span>
                          </div>
                          {/* FTP Options Submenu with Radio Buttons */}
                          {showFtpOptions && (
                            <div className="absolute left-full top-16 bg-white rounded-md shadow-lg z-20 border border-gray-200 p-3 w-48">
                              <div className="text-sm font-medium mb-2">
                                Select Period:
                              </div>

                              <div className="flex items-center mb-2">
                                <input
                                  type="radio"
                                  id="thisMonth"
                                  name="ftpPeriod"
                                  value="This Month"
                                  checked={ftpRefreshType === "This Month"}
                                  onChange={() =>
                                    setFtpRefreshType("This Month")
                                  }
                                  className="mr-2 accent-black"
                                />
                                <label
                                  htmlFor="thisMonth"
                                  className="text-sm text-gray-700 cursor-pointer"
                                >
                                  This Month
                                </label>
                              </div>

                              <div className="flex items-center mb-3">
                                <input
                                  type="radio"
                                  id="lastMonth"
                                  name="ftpPeriod"
                                  value="Last Month"
                                  checked={ftpRefreshType === "Last Month"}
                                  onChange={() =>
                                    setFtpRefreshType("Last Month")
                                  }
                                  className="mr-2 accent-black"
                                />
                                <label
                                  htmlFor="lastMonth"
                                  className="text-sm text-gray-700 cursor-pointer"
                                >
                                  Last Month
                                </label>
                              </div>

                              <button
                                className="w-full py-1.5 bg-bgSecondary text-white text-sm rounded hover:bg-opacity-90"
                                onClick={() => {
                                  setShowFtpOptions(false);
                                  setShowMenuDropdown(false);
                                  handleFtpRefresh(ftpRefreshType);
                                }}
                              >
                                Apply
                              </button>
                            </div>
                          )}
                        </>
                      )}
                  </div>
                </div>
              )}
            </div>

            {/* Info Tooltip */}
            <CssTooltip
              title={
                <div className="text-xs p-1">
                  <p className="mb-1.5">
                    During calendar selection, to view reports spanning more
                    than an hour, a day, a week, or a month, make sure to set
                    the start time to 00:00.
                  </p>
                  <p className="mb-1.5">
                    In case of selection of last 6, 12, 24 hours; reports will
                    be generated on an hourly basis with the start time of the
                    selected hour.
                  </p>
                  <p className="mb-1.5">
                    For example, if the current user time range is from 13:01 to
                    13:59, then the report will be generated from 13:00 hour
                    onwards.
                  </p>
                </div>
              }
              placement="top"
              arrow
            >
              <InfoIcon className="ml-2 mt-1 w-4 h-3.5" />
            </CssTooltip>

            {/* Calendar Picker - Disabled for weekly/monthly */}
            <div
              className={`${
                filters?.duration === "weekly" ||
                filters?.duration === "monthly"
                  ? "pointer-events-none opacity-50"
                  : ""
              }`}
              onClick={() => {
                setShowMenuDropdown(false);
              }}
            >
              <ReportCalendar
                selectedFilter={selectedFilter}
                setSelectedFilter={setSelectedFilter}
                setSelectedRange={setSelectedRange}
                selectedRange={selectedRange}
                reportTimeRange={getTimeRange(viewBy)}
                viewBy={viewBy}
                subtype={subtype}
                openDialog={openDialog}
                setOpenDialog={setOpenDialog}
                data={data}
                isAdmin={isAdmin}
              />
            </div>

            {/* Download Button */}
            <CssTooltip
              title={tooltipContent}
              placement="top"
              arrow
              PopperProps={{
                modifiers: [
                  {
                    name: "preventOverflow",
                    options: {
                      altBoundary: true,
                    },
                  },
                ],
              }}
              enterNextDelay={100}
              enterDelay={100}
              leaveDelay={200}
              componentsProps={{
                popper: {
                  sx: {
                    opacity: 1,
                  },
                },
              }}
            >
              <span style={{ display: "inline-block" }}>
                {" "}
                <Button
                  buttonClassName="text-xs w-32 text-white h-10 rounded-md"
                  label="Download"
                  onClick={() => {
                    if (permission === 0) {
                      setShowAlertConfirmation(true);
                      setMessage("Download permission not allowed");
                    } else {
                      setShowExportConfirmation(true);
                    }
                  }}
                  disabled={isDownloading}
                />
              </span>
            </CssTooltip>
          </div>
        </div>

        {/* Report Summary and Tables */}
        <div className="mx-3 mt-5">
          {/* Display Date Range if data exists */}
          {details.length > 0 && (
            <div className="mt-5 mx-1 flex items-center justify-between">
              <div className="text-sm text-black font-bold">
                Report from {selectedFilter?.startDate} to{" "}
                {selectedFilter?.endDate}
              </div>
            </div>
          )}

          {/* Aggregated Data Table */}
          {aggrgationDetail && (
            <div className="mt-3">
              <AggregationTable aggregateResponse={aggrgationDetail} />
            </div>
          )}

          {/* Report Table  */}
          {details.length > 0 ? (
            <>
              {/* Main Report Table */}
              <div className="mt-5">
                <ReportTable
                  columns={columns}
                  data={details}
                  isLoading={isLoading}
                />
              </div>

              {/* Pagination and Result Count */}
              <div className="flex items-center justify-between mt-5">
                <div className="flex items-center">
                  <ResultPerPageComponent
                    countPerPage={resultPerPage}
                    limit={limitPerPage}
                    handleLimitChange={handleLimitChange}
                    pageName="reports"
                  />
                  <div className="text-sm pl-3 text-titleColor">
                    {(currentPage - 1) * limitPerPage + 1} -{" "}
                    {Math.min(limitPerPage * currentPage, totalCount)} of{" "}
                    {totalCount} rows
                  </div>
                </div>

                <Pagination
                  className="pagination-bar"
                  currentPage={currentPage}
                  totalCount={totalCount}
                  pageSize={limitPerPage}
                  onPageChange={handlePageChange}
                />
              </div>
            </>
          ) : (
            !isLoading && (
              // Empty State UI
              <div className="border border-outerBorder mb-5 mt-8">
                <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                  Oops! No records to display.
                </div>
                <div className="flex justify-center my-10">
                  <img
                    src={bgImage}
                    className="h-[10%] w-[10%] object-cover"
                    alt="bg"
                  />
                </div>
              </div>
            )
          )}
        </div>
      </div>

      {/* Export Report Modal */}
      <ExportPopup
        show={showExportConfirmation}
        onHide={() => setShowExportConfirmation(false)}
        onConfirm={(type) => {
          exportReport(type);
          setShowExportConfirmation(false);
        }}
        title={"Export Report"}
        identity={"Reports"}
        exportPermissions={exportPermissions}
      />

      {/* Alert Modal for Download Permission */}
      <InfoModal
        show={showAlertConfirmation}
        onHide={() => {
          setShowAlertConfirmation(false);
        }}
        message={message}
      />

      {/* Send Mail Dialog */}
      <SendMail
        openGroupDialog={sendMailDialog}
        closeGroupDialog={() => {
          setSendMailDialog(false);
        }}
        selectedFilter={selectedFilter}
        searchStr={searchStr}
        type={extensionType}
        reportName={data}
        timeZone={timeZone}
      />

      {/* Toast Notifications */}
      <ToastContainer position="top-center" autoClose={3000} />

      {/* Error and Success Dialogs */}
      <ErrorDialog
        show={errorDialog}
        onHide={() => setErrorDialog(false)}
        message={message}
      />
      <SuccessDialog
        show={successDialog}
        onHide={() => setSuccessDialog(false)}
        message={message}
      />

      {/* Conditional Filter Dialog */}
      {filterDialog && (
        <MetaDataProvider>
          <ReportFilter
            openFilterDialog={filterDialog}
            closeFilterDialog={() => {
              setFilterDialog(false);
            }}
            reportName={data}
            setFilters={setFilters}
            isLoading={isLoading}
            filterData={filters}
            setLabelData={setLabelData}
          />
        </MetaDataProvider>
      )}
    </>
  );
}

export default StaticReports;
