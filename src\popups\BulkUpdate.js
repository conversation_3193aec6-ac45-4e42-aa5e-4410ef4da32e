import React, { useState } from "react";
import { Formik, Form, ErrorMessage } from "formik";
import Dialog from "@mui/material/Dialog";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import Box from "@mui/material/Box";
import Button from "../components/Button/OutlinedButton";
import CancelButton from "../components/Button/Button";
import { CloseIcon } from "../icons";
import Select from "../components/FormsUI/Select";
import CustomDropDown from "../components/Dropdown/CustomeDropdown";
import { alertServices, getAlertNameList } from "../services/alert-api";
import { useMutation, useQuery } from "react-query";
import SuccessDialog from "./SuccessDialog";
import * as Yup from "yup";
import theme from "../tailwind-theme";

export default function BulkUpdate({ closeDialog, openDialog }) {
  const [alertNameList, setAlertNameList] = useState([]);
  const { mutate: updateAlertAPI, isLoading: upadteLoading } = useMutation(
    alertServices.handleStatusUpdate
  );
  const [successDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");

  const statusOptions = [
    { label: "Open", value: "open" },
    { label: "Analyzing", value: "analyzing" },
    { label: "Fixed", value: "fixed" },
    { label: "Closed", value: "closed" },
  ];

  useQuery(["/getAlertsHistory"], getAlertNameList, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      console.log("res", res?.data);
      setAlertNameList(
        res?.data?.map((type) => ({ value: type.value, label: type.label })) ||
          []
      );
    },
  });
  const validationSchema = Yup.object().shape({
    alertName: Yup.array()
      .min(1, "Alert Name is required")
      .required("Alert Name is required"),
    status: Yup.string()
      .min(1, "Status is required")
      .required("Status is required"),
  });
  return (
    <Box width={"100%"}>
      <Formik
        initialValues={{
          alertName: "",
          status: "",
        }}
        validationSchema={validationSchema}
        enableReinitialize={true}
        onSubmit={(values) => {
          const transformedValues = {
            ...values,
            alertName: values.alertName
              .map((item) => item.split(",").map(Number))
              .flat(),
          };

          updateAlertAPI(
            {
              status: transformedValues.status,
              id: transformedValues.alertName,
            },
            {
              onSuccess: (data) => {
                setSuccessDialog(true);
                setMessage("Status Updated Successfully ");
                closeDialog();
              },
              onError: (error) => {
                setMessage(error);
                closeDialog();
              },
            }
          );
        }}
      >
        {({ setFieldValue, resetForm }) => {
          return (
            <>
              <Dialog
                open={openDialog}
                onClose={() => {
                  closeDialog();
                  resetForm();
                }}
                fullWidth
                onClick={(event) => {
                  if (event.target === event.currentTarget) {
                    closeDialog();
                    resetForm();
                  }
                }}
                sx={{
                  "& .MuiDialog-container": {
                    "& .MuiPaper-root": {
                      width: "100%",
                      maxWidth: "450px",
                      margin: 0,
                    },
                  },
                }}
              >
                <Form>
                  <div className="mx-4  mb-5">
                    <div className=" mt-4 text-black text-base font-medium flex items-center justify-between">
                      {"Bulk Update"}
                      <CloseIcon
                        onClick={() => {
                          closeDialog();
                          resetForm();
                        }}
                        cursor={"pointer"}
                        className=" w-2.5 h-2.5"
                      />
                    </div>

                    <div className="mt-2  mb-3 border-b border-panelBorder" />

                    <div className="w-full  mt-4">
                      <InputLabel label={"Alert Name"} isMandatory={true} />
                      <CustomDropDown
                        btnWidth="w-full"
                        data={alertNameList || []}
                        btnName={"Select Alert Name"}
                        onSelectionChange={(selectedDetails) => {
                          setFieldValue("alertName", selectedDetails);
                        }}
                        //   defaultSelectedData={formData["alertName"]}
                        isMultiSelect={true}
                      />
                      <p
                        style={{
                          fontSize: "12px",
                          color: theme.textColor.errorColor,
                        }}
                      >
                        <ErrorMessage name="alertName" />
                      </p>
                    </div>
                    <div className="w-full  mt-4">
                      <InputLabel label={"Status"} isMandatory={true} />
                      <Select
                        name="status"
                        options={statusOptions || []}
                        placeholder={"Select Status"}
                        isSearchable={true}
                        menuListzIndex={9999}
                        // maxMenuHeight={150}
                      />
                    </div>
                    <div>
                      <div className="text-center mt-10 gap-5 mb-2">
                        <CancelButton
                          onClick={() => {
                            setFieldValue("name", "");
                            closeDialog();
                            resetForm();
                          }}
                          label={"Cancel"}
                          buttonClassName="w-[100px] h-9 text-xs  "
                        ></CancelButton>
                        <Button
                          type="submit"
                          label={"Update"}
                          buttonClassName="w-[100px] h-9 text-xs ml-5"
                        ></Button>
                      </div>
                    </div>
                  </div>
                </Form>
              </Dialog>
            </>
          );
        }}
      </Formik>
      <SuccessDialog
        show={successDialog}
        onHide={() => setSuccessDialog(false)}
        message={message}
      />
    </Box>
  );
}
