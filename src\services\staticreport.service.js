import axios from "axios";
import getAPIMap from "../routes/ApiUrls";
import { removeSelectAllFromPayload } from "../utils/constants";

async function getReport(
  name,
  pageSize,
  currentPage,
  startDate,
  endDate,
  searchStr,
  timeZone,
  filters,
  duration
) {
  const payload = {
    reportName: name,
    download: 0,
    type: "",
    limit: currentPage,
    page: pageSize,
    startDate,
    endDate,
    search: searchStr,
    timezone: timeZone,
  };

  if (["Daily", "Weekly", "Monthly"].includes(duration)) {
    payload.defaultViewBy =
      duration === "Daily" ? "day" : duration === "Weekly" ? "week" : "month";
  }

  if (filters && filters.length !== 0) {
    payload.filters = filters;
  }

  const cleanedPayload = removeSelectAllFromPayload(payload);

  return axios.post(getAPIMap("reports"), cleanedPayload);
}

async function downloadReport(
  name,
  download,
  type,
  startDate,
  endDate,
  searchStr,
  timeZone,
  totalCount,
  filters,
  duration,
  maxCount,
  fileName
) {
  const axiosConfig = {};

  if (totalCount < maxCount) {
    axiosConfig.responseType = "blob";
  }

  const requestBody = {
    reportName: name,
    download: 1,
    type,
    startDate,
    endDate,
    search: searchStr,
    timezone: timeZone,
    fileName: fileName,
  };

  if (["Daily", "Weekly", "Monthly"].includes(duration)) {
    requestBody.defaultViewBy =
      duration === "Daily" ? "day" : duration === "Weekly" ? "week" : "month";
  }

  if (filters && filters.length !== 0) {
    requestBody.filters = filters;
  }
  return axios.post(getAPIMap("reports"), requestBody, axiosConfig);
}

async function getReportDetails(type, typename) {
  let url = getAPIMap("classifiedReports") + `?type=${type}`;
  if (typename !== "") {
    url += `&subtype=${typename}`;
  }
  return axios.get(url);
}
async function getReportFilter({ queryKey }) {
  let url = getAPIMap("reportsFilter") + `?reportName=${queryKey[1]}`;
  if (queryKey[2]) {
    url += `&visualizationType=${queryKey[2]}`;
  }
  return axios.get(url);
}
async function getCustomers() {
  let url = getAPIMap("customerData");
  return axios.get(url);
}
async function getLCRData() {
  let url = getAPIMap("lcrData");
  return axios.get(url);
}
async function getCDRStatus() {
  let url = getAPIMap("cdrStatusData");
  return axios.get(url);
}
async function getSuppliers() {
  let url = getAPIMap("supplierData");
  return axios.get(url);
}
async function getSourcePrime() {
  let url = getAPIMap("sourcePrimeFilter");
  return axios.get(url);
}
async function getDestinationPrime() {
  let url = getAPIMap("destinationPrimeFilter");
  return axios.get(url);
}
async function getDerivedField() {
  let url = getAPIMap("derivedFieldData");
  return axios.get(url);
}
export async function sendMail(options) {
  let url = getAPIMap("reports");
  let response = axios.post(url, options.payload);
  return response;
}
export const reportService = {
  getReport,
  getReportDetails,
  getCustomers,
  getSuppliers,
  downloadReport,
  getDerivedField,
  getLCRData,
  getCDRStatus,
  getReportFilter,
  getSourcePrime,
  getDestinationPrime,
};
