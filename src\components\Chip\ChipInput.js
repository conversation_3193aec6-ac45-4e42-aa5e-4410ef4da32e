import React, { useState, useEffect } from "react";
import "./ChipInput.css";
import theme from "../../tailwind-theme";

const ChipInput = ({ chips, onChipAdd, onChipRemove, isEdit, placeholder }) => {
  const [inputValue, setInputValue] = useState("");

  const handleInputChange = (event) => {
    setInputValue(event.target.value);
  };

  const handleInputKeyPress = (event) => {
    if (event.key === "Enter" && inputValue.trim() !== "") {
      onChipAdd(inputValue.trim());
      setInputValue("");
      event.preventDefault();
    }
  };

  useEffect(() => {
    const handleBackspace = (event) => {
      // event.preventDefault();
      if (event.key === "Backspace" && inputValue === "" && chips.length > 0) {
        event.preventDefault();
        onChipRemove(chips.length - 1);
      }
    };

    document.addEventListener("keydown", handleBackspace);

    return () => {
      document.removeEventListener("keydown", handleBackspace);
    };
  }, [inputValue, chips, onChipRemove]);

  return (
    <div className="input-container">
      <div className={`chips-input ${isEdit ? "" : "non-editable"}`}>
        {/* {console.log("inside chip", chips)} */}

        {chips?.map((chip, index) => (
          <span
            key={index}
            style={{
              backgroundColor: !isEdit
                ? "#eaeaeb"
                : theme.backgroundColor.bgTeritary,
            }}
            className="chip"
          >
            {/* {console.log("inside chip", chips, chip)} */}
            {chip}
            {isEdit && (
              <span className="close" onClick={() => onChipRemove(index)}>
                &times;
              </span>
            )}
          </span>
        ))}
        {isEdit && (
          <input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyPress={handleInputKeyPress}
            placeholder={placeholder || "Add a value..."}
          />
        )}
      </div>
    </div>
  );
};

export default ChipInput;
