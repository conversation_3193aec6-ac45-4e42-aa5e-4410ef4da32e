import React, { useState } from "react";

const MetricsTable = ({ aggregateResponse }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleTable = () => setIsExpanded((prev) => !prev);

  const tableData = Object.entries(aggregateResponse || {}).map(
    ([name, value]) => ({ name, value })
  );

  const columnsPerRow = 6;
  const rows = Math.ceil(tableData.length / columnsPerRow);

  const ChevronIcon = ({ direction = "down" }) => {
    return direction === "down" ? (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fillRule="evenodd"
          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
          clipRule="evenodd"
        />
      </svg>
    ) : (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fillRule="evenodd"
          d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
          clipRule="evenodd"
        />
      </svg>
    );
  };

  return (
    <div className="w-full text-xs">
      <div
        onClick={toggleTable}
        className="cursor-pointer px-1 py-1 font-semibold text-sm flex items-center gap-1 underline"
      >
        <span>Summary</span>
        <ChevronIcon direction={isExpanded ? "up" : "down"} />
      </div>

      {isExpanded && (
        <div className="mt-2 w-full overflow-x-auto border border-gray-300 rounded">
          <table className="w-full border-collapse">
            <tbody>
              {[...Array(rows)].map((_, rowIndex) => {
                const rowItems = tableData.slice(
                  rowIndex * columnsPerRow,
                  (rowIndex + 1) * columnsPerRow
                );

                return (
                  <React.Fragment key={rowIndex}>
                    {/* Header row */}
                    <tr className="bg-gray-200 text-black">
                      {rowItems.map((item, index) => (
                        <td
                          key={`name-${index}`}
                          className="border border-gray-300 px-3 py-2 font-semibold text-center"
                        >
                          {item.name}
                        </td>
                      ))}
                    </tr>
                    <tr className="bg-white">
                      {rowItems.map((item, index) => {
                        const [intPart, decimalPart] = item.value
                          .toString()
                          .split(".");
                        const formattedValue = `${Number(
                          intPart
                        ).toLocaleString("en-IN")}${
                          decimalPart ? "." + decimalPart : ""
                        }`;

                        return (
                          <td
                            key={`value-${index}`}
                            className="border border-gray-300 px-3 py-3 text-center"
                          >
                            {formattedValue}
                          </td>
                        );
                      })}
                    </tr>
                  </React.Fragment>
                );
              })}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default MetricsTable;
