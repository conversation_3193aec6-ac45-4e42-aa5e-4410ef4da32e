import React, { useState, useEffect, useContext } from "react";
import { CalendarIcon } from "../../icons";
import dayjs from "dayjs";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./styles.css";
import { DownArrowIcon, UpArrowIcon } from "../../icons";
import { timeRanges } from "../../utils/constants";
import { DataContext } from "../../context/DataContext";
import { AuthContext } from "../../context/AuthContext";

function ReportCalendar({
  selectedFilter,
  setSelectedFilter,
  setSelectedRange,
  selectedRange,
  reportTimeRange,
  viewBy,
  subtype,
  setOpenDialog,
  openDialog,
  data,
  isAdmin,
}) {
  const [isCalendarSelected, setIsCalendarSelected] = useState(false);
  const [dateRange, setDateRange] = useState([null, null]);
  const [startDate, endDate] = dateRange;
  const [startTime, setStartTime] = useState({
    hours: "00",
    minutes: "00",
    seconds: "00",
  });
  const [endTime, setEndTime] = useState({
    hours: "00",
    minutes: "00",
    seconds: "00",
  });
  const [selectedDuration, setSelectedDuration] = useState(
    selectedFilter.duration ? selectedFilter.duration : ""
  );

  const [availableDurations, setAvailableDurations] = useState([]);
  const [showDurationOptions, setShowDurationOptions] = useState(false);

  const [validationError, setValidationError] = useState("");
  const { configApiData } = useContext(AuthContext);

  const isFYRReport = configApiData.FY_REPORTS.includes(data) && isAdmin;

  const { setRangeCalendar, rangeCalendar } = useContext(DataContext);

  const startDateConfig = configApiData?.FYR_START_DATE;
  const endDateConfig = configApiData?.FYR_END_DATE;

  const validateDateRange = (start, end, duration) => {
    if (!start || !end) return "";

    const startDate = dayjs(start);
    const endDate = dayjs(end);
    const monthsDifference = endDate.diff(startDate, "month");

    if (duration === "Daily") {
      if (monthsDifference >= 2) {
        return "To choose daily give less than 2 months";
      }
    } else if (duration === "Weekly") {
      if (startDate.day() !== 1) {
        return "Weekly duration must start on Monday";
      }
      if (endDate.day() !== 0) {
        return "Weekly duration must end on Sunday";
      }
    } else if (duration === "Monthly") {
      if (startDate.date() !== 1) {
        return "Monthly duration must start at the beginning of the month";
      }

      if (
        endDate.date() !== endDate.daysInMonth() &&
        !endDate.isSame(dayjs(), "day")
      ) {
        return "Monthly duration must end at end of month or today";
      }
    }

    return "";
  };
  const isApplyDisabled =
    startDate === null || endDate === null || validationError !== "";

  useEffect(() => {
    if (dateRange[0] && dateRange[1]) {
      updateAvailableDurations(dateRange[0], dateRange[1]);
      const error = validateDateRange(
        dateRange[0],
        dateRange[1],
        selectedDuration
      );
      setValidationError(error);
    }
  }, [dateRange]);

  useEffect(() => {
    if (dateRange[0] && dateRange[1]) {
      const error = validateDateRange(
        dateRange[0],
        dateRange[1],
        selectedDuration
      );
      setValidationError(error);
    }
  }, [selectedDuration]);

  const updateAvailableDurations = (start, end) => {
    const startDate = dayjs(start);
    const endDate = dayjs(end);
    const daysDifference = endDate.diff(startDate, "day");
    const hoursDifference = endDate.diff(startDate, "hour");
    const minutesDifference = endDate.diff(startDate, "minute");

    if (hoursDifference <= 1 || minutesDifference <= 60) {
      setShowDurationOptions(false);
      return;
    } else if (hoursDifference <= 24 || daysDifference <= 1) {
      setShowDurationOptions(false);
      return;
    } else {
      setShowDurationOptions(true);
    }

    let newDurations = ["Daily"];

    if (daysDifference >= 7) {
      newDurations.push("Weekly");
    }

    const today = dayjs();
    const diffInDays = endDate.diff(startDate, "day") + 1;
    const isSameMonth =
      startDate.month() === endDate.month() &&
      startDate.year() === endDate.year();

    const isFullMonthRange =
      startDate.date() === 1 &&
      endDate.isSame(endDate.endOf("month"), "day") &&
      isSameMonth;

    const isFromStartToToday =
      startDate.date() === 1 &&
      endDate.isSame(today, "day") &&
      isSameMonth &&
      endDate.isSame(today, "month");

    const isThirtyDaysOrMore = diffInDays >= 30;

    if (isFullMonthRange || isFromStartToToday || isThirtyDaysOrMore) {
      newDurations.push("Monthly");
    }
    setAvailableDurations(newDurations);
  };

  const updateDurationOptionsForPredefinedRange = (selectedRange) => {
    const shortTermRanges = [
      "Last Hour",
      "Last 6 Hours",
      "Last 12 Hours",
      "Last 24 Hours",
      "Yesterday",
      "Today",
    ];
    const midTermRanges = ["Last Seven Days", "Last Week"];
    const longTermRanges = ["This Month", "Last 30 Days", "Last Month"];
    const yearly = ["This Year", "Last Year"];

    if (!selectedRange) {
      setAvailableDurations([]);
      setSelectedDuration("");
      return;
    }

    let newAvailableDurations = [];

    const viewByKey = viewBy.sort().join(",");

    switch (viewByKey) {
      case "hour":
        newAvailableDurations = [];
        break;

      case "day,month,week":
        if (yearly.includes(selectedRange)) {
          newAvailableDurations = ["Monthly"];
          setSelectedDuration("Monthly");
        } else {
          if (
            [
              "Today",
              "Yesterday",
              "Last Seven Days",
              "Last Week",
              "This Month",
              "Last 30 Days",
              "Last Month",
            ].includes(selectedRange)
          ) {
            newAvailableDurations = ["Daily"];
          }
          if (
            ["This Month", "Last 30 Days", "Last Month"].includes(selectedRange)
          ) {
            newAvailableDurations.push("Weekly", "Monthly");
          }
        }
        break;

      case "day,hour,month,week":
        if (shortTermRanges.includes(selectedRange)) {
          newAvailableDurations = [];
        } else if (
          midTermRanges.includes(selectedRange) ||
          longTermRanges.includes(selectedRange)
        ) {
          newAvailableDurations = ["Daily"];
        }
        if (
          ["This Month", "Last 30 Days", "Last Month"].includes(selectedRange)
        ) {
          newAvailableDurations.push("Weekly", "Monthly");
        }
        break;
      case "day,hour,minute,month,week":
        if (shortTermRanges.includes(selectedRange)) {
          newAvailableDurations = [];
        } else if (
          midTermRanges.includes(selectedRange) ||
          longTermRanges.includes(selectedRange)
        ) {
          newAvailableDurations = ["Daily"];
        }
        if (
          ["This Month", "Last 30 Days", "Last Month"].includes(selectedRange)
        ) {
          newAvailableDurations.push("Weekly", "Monthly");
        }
        break;

      default:
        newAvailableDurations = [];
        break;
    }

    setAvailableDurations(newAvailableDurations);
  };

  const handleTimeChange = (field, time, setTime) => (e) => {
    const { value } = e.target;
    let numericValue = value.replace(/\D/g, "");
    if (field === "hours") {
      if (parseInt(numericValue) > 23) {
        numericValue = "23";
      }
    } else if (field === "minutes") {
      if (parseInt(numericValue) > 59) {
        numericValue = "59";
      }
    }

    setTime((prevTime) => ({
      ...prevTime,
      [field]: numericValue,
    }));
  };

  const handleIncrement = (field, time, setTime, limit) => () => {
    setTime((prevTime) => {
      const newValue = (parseInt(prevTime[field], 10) + 1) % (limit + 1);
      const newTime = {
        ...prevTime,
        [field]: newValue < 10 ? `0${newValue}` : `${newValue}`,
      };
      return newTime;
    });
  };

  const handleDecrement = (field, time, setTime, limit) => () => {
    setTime((prevTime) => {
      const newValue = Math.max(
        (parseInt(prevTime[field], 10) - 1 + (limit + 1)) % (limit + 1),
        0
      );
      const newTime = {
        ...prevTime,
        [field]: newValue < 10 ? `0${newValue}` : `${newValue}`,
      };
      return newTime;
    });
  };

  const handleDateChange = (dates) => {
    setDateRange(dates);
    setIsCalendarSelected(selectedRange === "Calendar");
    setValidationError("");
  };

  function handleReset() {
    setStartTime({ hours: "00", minutes: "00", seconds: "00" });
    setEndTime({ hours: "00", minutes: "00", seconds: "00" });
    setDateRange([null, null]);
    setIsCalendarSelected(false);
    setValidationError("");
  }

  const handleItemClick = (range) => {
    setAvailableDurations([]);
    setSelectedDuration("");
    setSelectedRange(range);
    if (range === "Calendar") {
      setIsCalendarSelected(true);
    } else {
      setValidationError("");
      updateDurationOptionsForPredefinedRange(range);
    }
  };

  const handleDurationChange = (duration) => {
    setSelectedDuration(duration);
    if (dateRange[0] && dateRange[1]) {
      const error = validateDateRange(dateRange[0], dateRange[1], duration);
      setValidationError(error);
    }
  };

  const handleApply = () => {
    setOpenDialog(false);

    if (selectedRange) {
      let formattedStart, formattedEnd;
      const currentDateTime = dayjs();

      if (
        selectedRange === "Last Hour" ||
        selectedRange === "Last 6 Hours" ||
        selectedRange === "Last 12 Hours" ||
        selectedRange === "Last 24 Hours"
      ) {
        const hours = {
          "Last Hour": 1,
          "Last 6 Hours": 6,
          "Last 12 Hours": 12,
          "Last 24 Hours": 24,
        };

        const lastXHours = currentDateTime.subtract(
          hours[selectedRange],
          "hour"
        );
        if (selectedRange === "Last Hour") {
          formattedStart = lastXHours.format("YYYY-MM-DD HH:mm:ss");
          formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
        } else {
          formattedStart = lastXHours.format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
        }

        handleReset();
      } else if (selectedRange === "Today") {
        handleReset();
        formattedStart = currentDateTime
          .startOf("day")
          .format("YYYY-MM-DD HH:mm:ss");
        formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
      } else if (selectedRange === "Yesterday") {
        handleReset();
        const yesterday = currentDateTime.subtract(1, "day");
        formattedStart = yesterday.startOf("day").format("YYYY-MM-DD HH:mm:ss");
        formattedEnd = yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss");
      } else if (selectedRange === "Last Seven Days") {
        handleReset();
        formattedStart = currentDateTime
          .subtract(6, "days")
          .startOf("day")
          .format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
      } else if (selectedRange === "Last Week") {
        handleReset();
        formattedStart = currentDateTime
          .subtract(1, "week")
          .startOf("week")
          .format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime
          .subtract(1, "week")
          .endOf("week")
          .format("YYYY-MM-DD HH:00:00");
      } else if (selectedRange === "Last 30 Days") {
        handleReset();
        formattedStart = currentDateTime
          .subtract(29, "days")
          .startOf("day")
          .format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime
          .endOf("day")
          .format("YYYY-MM-DD HH:00:00");
      } else if (selectedRange === "Last Month") {
        handleReset();
        formattedStart = currentDateTime
          .subtract(1, "month")
          .startOf("month")
          .format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime
          .subtract(1, "month")
          .endOf("month")
          .format("YYYY-MM-DD HH:00:00");
      } else if (selectedRange === "This Month") {
        handleReset();
        formattedStart = currentDateTime
          .startOf("month")
          .format("YYYY-MM-DD HH:mm:ss");
        formattedEnd = currentDateTime
          .endOf("day")
          .format("YYYY-MM-DD HH:mm:ss");
      } else if (selectedRange === "This Year") {
        handleReset();
        formattedStart = startDateConfig;
        formattedEnd = endDateConfig;
      } else if (selectedRange === "Last Year") {
        handleReset();
        formattedStart = dayjs(startDateConfig)
          .subtract(1, "year")
          .startOf("year")
          .format("YYYY-MM-DD HH:mm:ss");
        formattedEnd = dayjs(endDateConfig)
          .subtract(1, "year")
          .endOf("year")
          .format("YYYY-MM-DD HH:mm:ss");
      } else if (selectedRange === "Calendar") {
        if (dateRange[0] !== null && dateRange[1] !== null) {
          const formattedStartTime = `${startTime.hours}:${startTime.minutes}:${startTime.seconds}`;
          const formattedEndTime = `${endTime.hours}:${endTime.minutes}:${endTime.seconds}`;

          formattedStart =
            dayjs(dateRange[0]).format("YYYY-MM-DD") + " " + formattedStartTime;
          formattedEnd =
            dayjs(dateRange[1]).format("YYYY-MM-DD") + " " + formattedEndTime;
        }
      }

      setSelectedFilter({
        startDate: formattedStart,
        endDate: formattedEnd,
        duration: selectedDuration ? selectedDuration : "",
        selectedRange: selectedRange,
      });
      setSelectedRange(selectedRange);
    }
  };

  useEffect(() => {
    updateDurationOptionsForPredefinedRange(selectedRange);
  }, [selectedRange]);

  return (
    <div className="relative">
      <div className="flex gap-4 items-center">
        <div
          className="px-3 w-full min-w-[180px] h-10 border border-calendarBoreder rounded-md text-titleColor flex cursor-pointer"
          onClick={() => {
            setOpenDialog(!openDialog);
          }}
        >
          <CalendarIcon className="w-5 h-5 mt-2 " />
          <div className="text-titleColor text-sm mt-2 ml-2 mr-5 whitespace-nowrap">
            {selectedRange}
          </div>
        </div>
      </div>

      {openDialog && (
        <div
          className={`absolute top-12
          ${rangeCalendar === "Calendar" ? "-right-40" : "right-0"}
           px-1 py-1 border border-outerBorder rounded-sm bg-white min-w-[200px] min-h-[218px] flex flex-row z-10`}
        >
          <div className="px-3 py-2 border border-outerBorder rounded-sm bg-white">
            <div className="mt-1 text-titleColor text-xs font-['Open Sans Hebrew'] whitespace-nowrap font-bold">
              {"Quick Access"}
            </div>
            <p className="mt-3 border-b border-[#D9D9D9]"></p>
            <div className="grid grid-cols-2 gap-1.5 mt-3 ">
              {timeRanges
                .filter((range) =>
                  isFYRReport
                    ? true
                    : range !== "This Year" && range !== "Last Year"
                )
                .map((range, index) => (
                  <button
                    key={index}
                    disabled={
                      reportTimeRange ? !reportTimeRange.includes(range) : false
                    }
                    onClick={() => {
                      handleItemClick(range);
                      setRangeCalendar(range);
                    }}
                    className={` text-left cursor-pointer disabled:text-opacity-40 text-tabColor text-[10px] font-['Open Sans Hebrew'] font-normal leading-5 ${
                      range === "Calendar" ? "col-span-2 " : ""
                    } ${selectedRange === range ? "bg-[#FDF5F5]" : ""} `}
                  >
                    {range}
                  </button>
                ))}
            </div>

            {validationError &&
              (selectedRange === "Calendar" || showDurationOptions) && (
                <div className="mt-2 text-red-500 text-xs">
                  {validationError}
                </div>
              )}

            <button
              className={`mt-2 w-[167px] h-5 bg-bgSecondary text-xs text-white rounded-[3px] mb-2 ${
                (selectedRange === "Calendar" && isApplyDisabled) ||
                validationError
                  ? "opacity-50 cursor-not-allowed"
                  : ""
              }`}
              onClick={handleApply}
              disabled={
                (selectedRange === "Calendar" && isApplyDisabled) ||
                !!validationError
              }
            >
              Apply Period
            </button>
          </div>

          {selectedRange === "Calendar" && (
            <div className=" ml-2 min-w-[455px]">
              <DatePicker
                isOutsideRange={() => false}
                startDate={startDate}
                endDate={endDate}
                maxDate={new Date()}
                monthsShown={2}
                selectsRange
                inline
                style={{ backgroundColor: "white", display: "inline-flex" }}
                onChange={handleDateChange}
              />
              <div className="relative border-b border-r border-l border-outerBorder -mt-1.5 mr-0.5 ">
                <div className="flex gap-14 mb-1">
                  <div className="flex items-center">
                    <div className="ml-3 text-titleColor text-xs font-['Open Sans Hebrew'] whitespace-nowrap font-bold items-center">
                      Start time
                    </div>
                    {["hours", "minutes"].map((field) => (
                      <div
                        key={`start-${field}`}
                        className="flex items-center ml-5"
                      >
                        <div className="px-1 h-7 border border-listBorder rounded-md text-[#707070] flex">
                          <input
                            name={field}
                            value={startTime[field]}
                            onChange={handleTimeChange(
                              field,
                              startTime,
                              setStartTime
                            )}
                            maxLength="2"
                            className="focus:outline-none w-5"
                          />
                        </div>
                        <div className="ml-1 flex flex-col gap-1">
                          <div
                            className="border border-listBorder h-2 flex items-center justify-center cursor-pointer"
                            onClick={handleIncrement(
                              field,
                              startTime,
                              setStartTime,
                              field === "hours" ? 23 : 59
                            )}
                          >
                            <UpArrowIcon />
                          </div>
                          <div
                            className="border border-listBorder h-2 flex items-center justify-center cursor-pointer"
                            onClick={handleDecrement(
                              field,
                              startTime,
                              setStartTime,
                              field === "hours" ? 23 : 59
                            )}
                          >
                            <DownArrowIcon />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center ">
                    <div className="text-titleColor text-xs font-['Open Sans Hebrew'] whitespace-nowrap font-bold items-center">
                      End time
                    </div>
                    {["hours", "minutes"].map((field) => (
                      <div
                        key={`end-${field}`}
                        className="flex ml-5 items-center"
                      >
                        <div className="px-1 h-7 border border-listBorder rounded-md text-[#707070] flex">
                          <input
                            name={field}
                            value={endTime[field]}
                            onChange={handleTimeChange(
                              field,
                              endTime,
                              setEndTime
                            )}
                            maxLength="2"
                            className="focus:outline-none w-5"
                          />
                        </div>
                        <div className="ml-1 flex flex-col gap-1">
                          <div
                            className="border border-listBorder h-2 flex items-center justify-center cursor-pointer"
                            onClick={handleIncrement(
                              field,
                              endTime,
                              setEndTime,
                              field === "hours" ? 23 : 59
                            )}
                          >
                            <UpArrowIcon />
                          </div>
                          <div
                            className="border border-listBorder h-2 flex items-center justify-center cursor-pointer"
                            onClick={handleDecrement(
                              field,
                              endTime,
                              setEndTime,
                              field === "hours" ? 23 : 59
                            )}
                          >
                            <DownArrowIcon />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex gap-14 mb-1">
                  <div className="flex gap-3 ml-[90px]">
                    <div className=" text-xs text-[#707070] font-['Open Sans Hebrew'] font-bold">
                      Hrs
                    </div>
                    <div className="ml-6 text-xs text-[#707070] font-['Open Sans Hebrew'] font-bold">
                      Min
                    </div>
                  </div>
                  <div className="flex gap-3 ml-20">
                    <div className="ml-2 text-xs text-[#707070] font-['Open Sans Hebrew'] font-bold">
                      Hrs
                    </div>
                    <div className="ml-6 text-xs text-[#707070] font-['Open Sans Hebrew'] font-bold">
                      Min
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <div className="w-28 p-4 border-l border-outerBorder">
            <div className="text-titleColor text-xs font-bold mb-3">
              Duration
            </div>
            {availableDurations.length === 0 ? (
              <div className="text-xs text-gray-500 italic">
                Not available for this selection
              </div>
            ) : (
              <div className="flex flex-col gap-3">
                {["Daily", "Weekly", "Monthly"].map((duration) => {
                  const isDisabled = !availableDurations.includes(duration);

                  return (
                    <label
                      key={duration}
                      className={`flex items-center ${
                        isDisabled
                          ? "opacity-50 cursor-not-allowed"
                          : "cursor-pointer"
                      }`}
                    >
                      <input
                        type="radio"
                        id={duration}
                        name="duration"
                        value={duration}
                        disabled={isDisabled}
                        checked={selectedDuration === duration}
                        onChange={() => handleDurationChange(duration)}
                        className="mr-2 accent-black"
                      />
                      <span className="text-tabColor text-xs">{duration}</span>
                    </label>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default ReportCalendar;
