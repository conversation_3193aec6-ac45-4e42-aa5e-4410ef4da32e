import React, { useState } from "react";
import Dialog from "@mui/material/Dialog";
import Box from "@mui/material/Box";
import { CloseIcon, InfoIcon } from "../icons";
import PanelVisualization from "../pages/DashboardManagement/PanelVisualization";
import { CssTooltip } from "../components/StyledComponent";
import { ConditionDisplay } from "../common/constants";
import { handleDateFormat } from "../common/commonFunctions";

export default function ExpandDashboard({ handleClose, open, panelData }) {
  const colors = ["#EDDF82", "#82C3ED", "#82EDAD", "#ED8282"];

  return (
    <Box width={"100%"}>
      <Dialog
        open={open}
        onClose={handleClose}
        fullWidth
        onClick={(event) => {
          if (event.target === event.currentTarget) {
            handleClose();
          }
        }}
        sx={{
          "& .MuiDialog-container": {
            "& .MuiPaper-root": {
              width: "100%",
              maxWidth: "1000px", // Set your width here
              margin: 0,
            },
          },
        }}
      >
        <div className="mx-4  mb-5">
          {/* {console.log("panelData", panelData)} */}
          <div className=" mt-4 text-black flex items-center justify-end">
            <CloseIcon
              onClick={handleClose}
              className=" w-2.5 h-2.5 cursor-pointer"
            />
          </div>
          <div className="text-xs text-titleColor ml-2 whitespace-pre-wrap break-all flex-grow">
            {panelData?.panelData?.name}
          </div>

          <div className="border-b border-panelBorder mb-4" />
          <div className="flex flex-col mb-2 mx-2">
            <div className="text-headingColor text-xs font-medium mb-2 flex justify-between">
              <div>
                {"From: " +
                  (panelData?.panelData?.startDate ||
                    panelData?.startDate ||
                    "")}
                {" - "}
                {"To: " +
                  (panelData?.panelData?.endDate || panelData?.endDate || "")}
              </div>
              {panelData?.panelData?.visualizationType === "Table Report" ? (
                <div>
                  {" No. of records : "}
                  {panelData?.count}
                </div>
              ) : null}
            </div>
            <div className="text-headingColor text-xs font-medium flex items-center">
              <span>Selected Filters:</span>
              <CssTooltip
                title={
                  <ConditionDisplay
                    conditions={panelData?.panelData?.filters}
                  />
                }
                placement="left"
                arrow
              >
                <InfoIcon className="ml-2 w-4 h-3.5" />
              </CssTooltip>
            </div>
          </div>

          <PanelVisualization
            type={panelData?.panelData?.visualizationType}
            data={panelData?.panelData?.data}
            colors={colors}
            isChart={true}
            isWidth={true}
            maxWidthh={"60vh"}
          />
        </div>
      </Dialog>
    </Box>
  );
}
